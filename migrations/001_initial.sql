-- Initial database schema for RustScan Web
-- This creates the core tables for task management and result storage

-- Tasks table - stores scanning tasks
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    target TEXT NOT NULL,
    scan_type VARCHAR(50) NOT NULL DEFAULT 'standard',
    config JSON NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT
);

-- Scan results table - stores results from various scanning tools
CREATE TABLE IF NOT EXISTS scan_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    tool VARCHAR(50) NOT NULL,
    target VARCHAR(255) NOT NULL,
    result_type VARCHAR(50) NOT NULL,
    data JSON NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- System configuration table - stores application settings
CREATE TABLE IF NOT EXISTS system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_scan_results_task_id ON scan_results(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_tool ON scan_results(tool);
CREATE INDEX IF NOT EXISTS idx_scan_results_target ON scan_results(target);

-- Insert default system configuration
INSERT OR IGNORE INTO system_config (key, value, description) VALUES
('max_concurrent_tasks', '5', 'Maximum number of concurrent scanning tasks'),
('default_timeout', '5000', 'Default timeout for scanning operations in milliseconds'),
('enable_notifications', 'true', 'Enable WebSocket notifications'),
('log_level', 'info', 'Application log level'),
('database_version', '1', 'Database schema version');

-- Create trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_tasks_timestamp 
    AFTER UPDATE ON tasks
    FOR EACH ROW
BEGIN
    UPDATE tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_config_timestamp 
    AFTER UPDATE ON system_config
    FOR EACH ROW
BEGIN
    UPDATE system_config SET updated_at = CURRENT_TIMESTAMP WHERE key = NEW.key;
END;

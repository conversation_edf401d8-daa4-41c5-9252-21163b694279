# RustScan Web 安全扫描平台 - 架构设计文档

## 🎯 架构概述

基于对现有RustScan CLI工具的深入分析，设计一个现代化的Web安全扫描平台，保留核心扫描能力的同时，提供Web界面和API服务。

## 📊 核心组件分析

### 可复用的核心组件

1. **Scanner模块** (`src/scanner/mod.rs`)
   - 核心扫描引擎，支持TCP/UDP扫描
   - 异步并发扫描能力
   - 可配置的批处理大小和超时
   - **复用方式**: 封装为服务层，提供异步扫描接口

2. **PortStrategy模块** (`src/port_strategy/mod.rs`)
   - 端口扫描策略（串行/随机/手动）
   - 端口范围生成和管理
   - **复用方式**: 直接复用，作为扫描配置的一部分

3. **Address解析模块** (`src/address.rs`)
   - IP地址、CIDR、域名解析
   - DNS解析器配置
   - 文件输入支持
   - **复用方式**: 封装为工具函数，供Web API调用

4. **Input配置模块** (`src/input.rs`)
   - 扫描参数配置
   - 配置文件解析
   - **复用方式**: 转换为Web API的请求/响应模型

5. **Scripts脚本引擎** (`src/scripts/mod.rs`)
   - 多语言脚本支持
   - 模板化命令执行
   - **复用方式**: 扩展为工作流引擎的一部分

## 🏗️ 新架构设计

### 项目结构

```
rustscan-web/
├── Cargo.toml                    # 项目配置
├── migrations/                   # 数据库迁移文件
│   ├── 001_initial.sql
│   └── 002_add_indexes.sql
├── src/
│   ├── lib.rs                   # 库入口，暴露核心功能
│   ├── main.rs                  # Web服务器入口
│   │
│   ├── core/                    # 核心扫描功能（复用现有代码）
│   │   ├── mod.rs
│   │   ├── scanner.rs           # 扫描器（基于现有Scanner）
│   │   ├── port_strategy.rs     # 端口策略（复用现有）
│   │   ├── address.rs           # 地址解析（复用现有）
│   │   └── config.rs            # 配置管理（基于现有Input）
│   │
│   ├── web/                     # Web应用层
│   │   ├── mod.rs
│   │   ├── server.rs            # Actix-web服务器配置
│   │   ├── routes/              # 路由定义
│   │   │   ├── mod.rs
│   │   │   ├── tasks.rs         # 任务管理API
│   │   │   ├── results.rs       # 结果查询API
│   │   │   ├── config.rs        # 配置管理API
│   │   │   └── websocket.rs     # WebSocket实时通信
│   │   ├── handlers/            # 请求处理器
│   │   │   ├── mod.rs
│   │   │   ├── task_handler.rs
│   │   │   ├── result_handler.rs
│   │   │   └── ws_handler.rs
│   │   ├── middleware/          # 中间件
│   │   │   ├── mod.rs
│   │   │   ├── auth.rs          # 认证中间件
│   │   │   ├── cors.rs          # CORS处理
│   │   │   └── logging.rs       # 请求日志
│   │   └── models/              # Web数据模型
│   │       ├── mod.rs
│   │       ├── request.rs       # 请求模型
│   │       ├── response.rs      # 响应模型
│   │       └── dto.rs           # 数据传输对象
│   │
│   ├── services/                # 业务逻辑层
│   │   ├── mod.rs
│   │   ├── task_service.rs      # 任务管理服务
│   │   ├── scan_service.rs      # 扫描执行服务
│   │   ├── result_service.rs    # 结果处理服务
│   │   └── notification_service.rs # 通知服务
│   │
│   ├── tools/                   # 扫描工具集成
│   │   ├── mod.rs
│   │   ├── rustscan.rs          # RustScan集成（基于core模块）
│   │   ├── nmap.rs              # Nmap集成
│   │   ├── dnsx.rs              # DNSx集成
│   │   ├── subfinder.rs         # Subfinder集成
│   │   ├── httpx.rs             # HTTPx集成
│   │   ├── nuclei.rs            # Nuclei集成
│   │   ├── workflow.rs          # 工作流协调器
│   │   └── results.rs           # 结果分析器
│   │
│   ├── database/                # 数据库层
│   │   ├── mod.rs
│   │   ├── connection.rs        # 数据库连接管理
│   │   ├── models/              # 数据库模型
│   │   │   ├── mod.rs
│   │   │   ├── task.rs          # 任务模型
│   │   │   ├── result.rs        # 结果模型
│   │   │   └── config.rs        # 配置模型
│   │   └── repositories/        # 数据访问层
│   │       ├── mod.rs
│   │       ├── task_repo.rs
│   │       ├── result_repo.rs
│   │       └── config_repo.rs
│   │
│   ├── common/                  # 公共模块
│   │   ├── mod.rs
│   │   ├── error.rs             # 错误处理
│   │   ├── config.rs            # 应用配置
│   │   ├── user_agent.rs        # User-Agent管理
│   │   └── utils.rs             # 工具函数
│   │
│   └── tests/                   # 测试模块
│       ├── mod.rs
│       ├── integration/         # 集成测试
│       └── unit/                # 单元测试
│
├── frontend/                    # React前端应用
│   ├── package.json
│   ├── vite.config.ts
│   ├── src/
│   │   ├── main.tsx
│   │   ├── App.tsx
│   │   ├── api/                 # API客户端
│   │   ├── components/          # React组件
│   │   ├── pages/               # 页面组件
│   │   ├── hooks/               # 自定义Hooks
│   │   ├── types/               # TypeScript类型
│   │   ├── utils/               # 工具函数
│   │   └── styles/              # 样式文件
│   └── public/
│
├── docker/                      # Docker配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── nginx.conf
│
└── docs/                        # 文档
    ├── API.md
    ├── DEPLOYMENT.md
    └── DEVELOPMENT.md
```

### 技术栈选择

#### 后端技术栈
- **Web框架**: Actix-web 4.x - 高性能异步Web框架
- **数据库**: SQLx + SQLite - 类型安全的数据库操作
- **异步运行时**: Tokio - 与现有代码兼容
- **序列化**: Serde - 复用现有配置
- **错误处理**: Anyhow + thiserror - 统一错误处理
- **日志**: tracing + tracing-subscriber - 结构化日志
- **WebSocket**: actix-web-actors - 实时通信

#### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite - 快速开发和构建
- **UI库**: Ant Design - 企业级组件库
- **状态管理**: Zustand - 轻量级状态管理
- **数据获取**: React Query - 数据缓存和同步
- **路由**: React Router - 客户端路由
- **WebSocket**: 原生WebSocket API

### 数据库设计

#### 核心表结构

```sql
-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    target TEXT NOT NULL,
    scan_type VARCHAR(50) NOT NULL,
    config JSON NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- 扫描结果表
CREATE TABLE scan_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    tool VARCHAR(50) NOT NULL,
    target VARCHAR(255) NOT NULL,
    result_type VARCHAR(50) NOT NULL,
    data JSON NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);

-- 系统配置表
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 工作流设计

### 扫描流程

```
用户请求 → 参数验证 → 创建任务 → 工作流调度 → 工具执行 → 结果收集 → 数据存储 → 通知推送
    ↓         ↓        ↓        ↓         ↓        ↓        ↓        ↓
  Web API   Validator  TaskService  Workflow   Tools   ResultService  DB   WebSocket
```

### 扫描模式

1. **快速扫描**: 常用端口 + 基础漏洞检测
2. **标准扫描**: 全端口 + 服务识别 + 常见漏洞
3. **深度扫描**: 全端口 + 详细服务识别 + 全面漏洞扫描
4. **Web专项**: HTTP/HTTPS + Web漏洞 + 爬虫分析
5. **自定义扫描**: 用户自定义参数和工具组合

## 📡 API设计

### RESTful API端点

```
POST   /api/tasks              # 创建扫描任务
GET    /api/tasks              # 获取任务列表
GET    /api/tasks/{id}         # 获取任务详情
DELETE /api/tasks/{id}         # 删除任务
POST   /api/tasks/{id}/stop    # 停止任务

GET    /api/results            # 获取扫描结果
GET    /api/results/{id}       # 获取结果详情
POST   /api/results/export     # 导出结果

GET    /api/config             # 获取系统配置
PUT    /api/config             # 更新系统配置

WebSocket /ws                  # 实时通信
```

### WebSocket消息格式

```json
{
  "type": "task_status",
  "task_id": 123,
  "status": "running",
  "progress": 45,
  "message": "正在扫描端口..."
}
```

## 🔧 核心服务设计

### TaskService (任务管理服务)
- 任务创建、更新、删除
- 任务状态管理
- 任务调度和优先级

### ScanService (扫描执行服务)
- 工作流协调
- 工具调用管理
- 进度跟踪

### ResultService (结果处理服务)
- 结果收集和解析
- 数据标准化
- 报告生成

### NotificationService (通知服务)
- WebSocket实时推送
- 任务状态变更通知
- 错误告警

这个架构设计充分复用了现有RustScan的核心能力，同时提供了现代化的Web界面和API服务。

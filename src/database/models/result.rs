//! Scan result database model

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// Scan result database model
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ScanResult {
    pub id: i64,
    pub task_id: i64,
    pub tool: String,
    pub target: String,
    pub result_type: String,
    pub data: String, // JSON serialized data
    pub created_at: DateTime<Utc>,
}

/// New scan result for insertion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewScanResult {
    pub task_id: i64,
    pub tool: String,
    pub target: String,
    pub result_type: String,
    pub data: String,
}

impl NewScanResult {
    pub fn new(task_id: i64, tool: String, target: String, result_type: String, data: String) -> Self {
        Self {
            task_id,
            tool,
            target,
            result_type,
            data,
        }
    }
}

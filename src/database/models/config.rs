//! System configuration database model

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// System configuration database model
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct SystemConfig {
    pub key: String,
    pub value: String,
    pub description: Option<String>,
    pub updated_at: DateTime<Utc>,
}

/// New system configuration for insertion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewSystemConfig {
    pub key: String,
    pub value: String,
    pub description: Option<String>,
}

impl NewSystemConfig {
    pub fn new(key: String, value: String, description: Option<String>) -> Self {
        Self {
            key,
            value,
            description,
        }
    }
}

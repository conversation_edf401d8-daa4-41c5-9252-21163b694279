//! Task database model
//!
//! This module defines the task model for storing scanning tasks.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// Task status enumeration
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "VARCHAR", rename_all = "lowercase")]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

impl std::fmt::Display for TaskStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskStatus::Pending => write!(f, "pending"),
            TaskStatus::Running => write!(f, "running"),
            TaskStatus::Completed => write!(f, "completed"),
            TaskStatus::Failed => write!(f, "failed"),
            TaskStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

impl std::str::FromStr for TaskStatus {
    type Err = String;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "pending" => Ok(TaskStatus::Pending),
            "running" => Ok(TaskStatus::Running),
            "completed" => Ok(TaskStatus::Completed),
            "failed" => Ok(TaskStatus::Failed),
            "cancelled" => Ok(TaskStatus::Cancelled),
            _ => Err(format!("Invalid task status: {}", s)),
        }
    }
}

/// Task database model
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: i64,
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub config: String, // JSON serialized ScanConfig
    pub status: String,
    pub progress: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
}

impl Task {
    /// Get task status as enum
    pub fn get_status(&self) -> Result<TaskStatus, String> {
        self.status.parse()
    }
    
    /// Set task status from enum
    pub fn set_status(&mut self, status: TaskStatus) {
        self.status = status.to_string();
        self.updated_at = Utc::now();
        
        match status {
            TaskStatus::Running => {
                if self.started_at.is_none() {
                    self.started_at = Some(Utc::now());
                }
            }
            TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled => {
                if self.completed_at.is_none() {
                    self.completed_at = Some(Utc::now());
                }
            }
            _ => {}
        }
    }
    
    /// Get task duration if completed
    pub fn get_duration(&self) -> Option<chrono::Duration> {
        if let (Some(started), Some(completed)) = (self.started_at, self.completed_at) {
            Some(completed - started)
        } else {
            None
        }
    }
    
    /// Check if task is in a terminal state
    pub fn is_terminal(&self) -> bool {
        matches!(
            self.get_status().unwrap_or(TaskStatus::Pending),
            TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled
        )
    }
    
    /// Check if task is active
    pub fn is_active(&self) -> bool {
        matches!(
            self.get_status().unwrap_or(TaskStatus::Pending),
            TaskStatus::Pending | TaskStatus::Running
        )
    }
}

/// New task for insertion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewTask {
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub config: String, // JSON serialized ScanConfig
}

impl NewTask {
    /// Create a new task
    pub fn new(name: String, target: String, scan_type: String, config: String) -> Self {
        Self {
            name,
            target,
            scan_type,
            config,
        }
    }
    
    /// Validate the new task
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("Task name cannot be empty".to_string());
        }
        
        if self.target.trim().is_empty() {
            return Err("Target cannot be empty".to_string());
        }
        
        if self.scan_type.trim().is_empty() {
            return Err("Scan type cannot be empty".to_string());
        }
        
        // Try to parse config as JSON to validate
        serde_json::from_str::<serde_json::Value>(&self.config)
            .map_err(|e| format!("Invalid config JSON: {}", e))?;
        
        Ok(())
    }
}

/// Task update model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskUpdate {
    pub status: Option<TaskStatus>,
    pub progress: Option<i32>,
    pub error_message: Option<String>,
}

impl TaskUpdate {
    /// Create a status update
    pub fn status(status: TaskStatus) -> Self {
        Self {
            status: Some(status),
            progress: None,
            error_message: None,
        }
    }
    
    /// Create a progress update
    pub fn progress(progress: i32) -> Self {
        Self {
            status: None,
            progress: Some(progress),
            error_message: None,
        }
    }
    
    /// Create an error update
    pub fn error(status: TaskStatus, error_message: String) -> Self {
        Self {
            status: Some(status),
            progress: None,
            error_message: Some(error_message),
        }
    }
}

/// Task summary for listing
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TaskSummary {
    pub id: i64,
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub status: String,
    pub progress: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TaskSummary {
    /// Get task status as enum
    pub fn get_status(&self) -> Result<TaskStatus, String> {
        self.status.parse()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_status_conversion() {
        assert_eq!("pending", TaskStatus::Pending.to_string());
        assert_eq!("running", TaskStatus::Running.to_string());
        assert_eq!("completed", TaskStatus::Completed.to_string());
        
        assert_eq!(TaskStatus::Pending, "pending".parse().unwrap());
        assert_eq!(TaskStatus::Running, "RUNNING".parse().unwrap());
        assert!("invalid".parse::<TaskStatus>().is_err());
    }

    #[test]
    fn test_new_task_validation() {
        let valid_task = NewTask::new(
            "Test Task".to_string(),
            "127.0.0.1".to_string(),
            "standard".to_string(),
            "{}".to_string(),
        );
        assert!(valid_task.validate().is_ok());
        
        let invalid_task = NewTask::new(
            "".to_string(),
            "127.0.0.1".to_string(),
            "standard".to_string(),
            "{}".to_string(),
        );
        assert!(invalid_task.validate().is_err());
    }

    #[test]
    fn test_task_state_checks() {
        let mut task = Task {
            id: 1,
            name: "Test".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "standard".to_string(),
            config: "{}".to_string(),
            status: "pending".to_string(),
            progress: 0,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            started_at: None,
            completed_at: None,
            error_message: None,
        };
        
        assert!(task.is_active());
        assert!(!task.is_terminal());
        
        task.set_status(TaskStatus::Completed);
        assert!(!task.is_active());
        assert!(task.is_terminal());
        assert!(task.completed_at.is_some());
    }
}

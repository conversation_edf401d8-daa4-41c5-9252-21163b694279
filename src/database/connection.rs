//! Database connection management
//!
//! This module handles SQLite database connections and connection pooling.

use sqlx::{Pool, Sqlite, SqlitePool, Row};
use std::time::Duration;
use tracing::{info, debug};

use crate::common::error::{AppError, Result};

/// Database connection pool type alias
pub type DatabasePool = Pool<Sqlite>;

/// Create and configure a database connection pool
pub async fn get_database_pool(database_url: &str) -> Result<DatabasePool> {
    info!("Connecting to database: {}", database_url);
    
    let options = sqlx::sqlite::SqliteConnectOptions::new()
        .filename(database_url.strip_prefix("sqlite:").unwrap_or(database_url))
        .create_if_missing(true)
        .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
        .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
        .busy_timeout(Duration::from_secs(30));

    let pool = SqlitePool::connect_with(options)
        .await
        .map_err(|e| AppError::database(format!("Failed to connect to database: {}", e)))?;
    
    debug!("Database connection pool created successfully");
    Ok(pool)
}

/// Test database connection
pub async fn test_connection(pool: &DatabasePool) -> Result<()> {
    sqlx::query("SELECT 1")
        .execute(pool)
        .await
        .map_err(|e| AppError::database(format!("Database connection test failed: {}", e)))?;
    
    debug!("Database connection test successful");
    Ok(())
}

/// Get database version and info
pub async fn get_database_info(pool: &DatabasePool) -> Result<DatabaseInfo> {
    let row = sqlx::query("SELECT sqlite_version() as version")
        .fetch_one(pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get database info: {}", e)))?;
    
    let version: String = row.get("version");
    
    // Get database size
    let size_row = sqlx::query("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
        .fetch_one(pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get database size: {}", e)))?;
    
    let size_bytes: i64 = size_row.get("size");
    
    Ok(DatabaseInfo {
        version,
        size_bytes: size_bytes as u64,
    })
}

/// Database information
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DatabaseInfo {
    pub version: String,
    pub size_bytes: u64,
}

/// Initialize database with required tables and indexes
pub async fn initialize_database(pool: &DatabasePool) -> Result<()> {
    info!("Initializing database schema");
    
    // Run migrations
    sqlx::migrate!("./migrations")
        .run(pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to run migrations: {}", e)))?;
    
    info!("Database schema initialized successfully");
    Ok(())
}

/// Clean up old data based on retention policy
pub async fn cleanup_old_data(pool: &DatabasePool, retention_days: u32) -> Result<u64> {
    let cutoff_date = chrono::Utc::now() - chrono::Duration::days(retention_days as i64);
    
    // Clean up old tasks and their results
    let result = sqlx::query(
        "DELETE FROM tasks WHERE created_at < ? AND status IN ('completed', 'failed', 'cancelled')"
    )
    .bind(cutoff_date)
    .execute(pool)
    .await
    .map_err(|e| AppError::database(format!("Failed to cleanup old data: {}", e)))?;
    
    let deleted_count = result.rows_affected();
    
    if deleted_count > 0 {
        info!("Cleaned up {} old tasks", deleted_count);
        
        // Vacuum database to reclaim space
        sqlx::query("VACUUM")
            .execute(pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to vacuum database: {}", e)))?;
        
        debug!("Database vacuumed successfully");
    }
    
    Ok(deleted_count)
}

/// Get database statistics
pub async fn get_database_stats(pool: &DatabasePool) -> Result<DatabaseStats> {
    // Count tasks by status
    let task_stats = sqlx::query(
        "SELECT status, COUNT(*) as count FROM tasks GROUP BY status"
    )
    .fetch_all(pool)
    .await
    .map_err(|e| AppError::database(format!("Failed to get task stats: {}", e)))?;
    
    let mut stats = DatabaseStats::default();
    
    for row in task_stats {
        let status: String = row.get("status");
        let count: i64 = row.get("count");
        
        match status.as_str() {
            "pending" => stats.pending_tasks = count as u64,
            "running" => stats.running_tasks = count as u64,
            "completed" => stats.completed_tasks = count as u64,
            "failed" => stats.failed_tasks = count as u64,
            "cancelled" => stats.cancelled_tasks = count as u64,
            _ => {}
        }
    }
    
    // Count total results
    let result_count: (i64,) = sqlx::query_as("SELECT COUNT(*) FROM scan_results")
        .fetch_one(pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get result count: {}", e)))?;
    
    stats.total_results = result_count.0 as u64;
    
    Ok(stats)
}

/// Database statistics
#[derive(Debug, Default, Clone, serde::Serialize, serde::Deserialize)]
pub struct DatabaseStats {
    pub pending_tasks: u64,
    pub running_tasks: u64,
    pub completed_tasks: u64,
    pub failed_tasks: u64,
    pub cancelled_tasks: u64,
    pub total_results: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_database_connection() {
        let temp_file = NamedTempFile::new().unwrap();
        let db_url = format!("sqlite:{}", temp_file.path().display());
        
        let pool = get_database_pool(&db_url).await.unwrap();
        test_connection(&pool).await.unwrap();
        
        let info = get_database_info(&pool).await.unwrap();
        assert!(!info.version.is_empty());
    }

    #[tokio::test]
    async fn test_database_stats() {
        let temp_file = NamedTempFile::new().unwrap();
        let db_url = format!("sqlite:{}", temp_file.path().display());
        
        let pool = get_database_pool(&db_url).await.unwrap();
        initialize_database(&pool).await.unwrap();
        
        let stats = get_database_stats(&pool).await.unwrap();
        assert_eq!(stats.pending_tasks, 0);
        assert_eq!(stats.total_results, 0);
    }
}

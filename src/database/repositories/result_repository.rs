//! Result repository implementation

use sqlx::{Row, SqlitePool};
use tracing::{debug, info};

use crate::common::error::{AppError, Result};
use crate::database::models::{New<PERSON>canR<PERSON>ult, ScanResult};

#[derive(Debug, Clone)]
pub struct ResultRepository {
    pool: SqlitePool,
}

impl ResultRepository {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn create(&self, new_result: &NewScanResult) -> Result<ScanResult> {
        debug!("Creating scan result for task: {}", new_result.task_id);

        let result = sqlx::query_as::<_, ScanResult>(
            r#"
            INSERT INTO scan_results (task_id, tool, target, result_type, data, created_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            RETURNING *
            "#,
        )
        .bind(new_result.task_id)
        .bind(&new_result.tool)
        .bind(&new_result.target)
        .bind(&new_result.result_type)
        .bind(&new_result.data)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create result: {}", e)))?;

        Ok(result)
    }

    pub async fn get_by_task_id(&self, task_id: i64) -> Result<Vec<ScanResult>> {
        debug!("Getting results for task: {}", task_id);

        let results = sqlx::query_as::<_, ScanResult>(
            "SELECT * FROM scan_results WHERE task_id = ? ORDER BY created_at",
        )
        .bind(task_id)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get results: {}", e)))?;

        Ok(results)
    }

    pub async fn delete_by_task_id(&self, task_id: i64) -> Result<u64> {
        info!("Deleting results for task: {}", task_id);

        let result = sqlx::query("DELETE FROM scan_results WHERE task_id = ?")
            .bind(task_id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to delete results: {}", e)))?;

        Ok(result.rows_affected())
    }
}

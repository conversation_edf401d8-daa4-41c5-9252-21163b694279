//! Task repository implementation
//!
//! This module provides database operations for tasks.

use sqlx::{Row, SqlitePool};
use tracing::{debug, info};

use crate::common::error::{AppError, Result};
use crate::database::models::{NewTask, Task, TaskSummary};

/// Task repository for database operations
#[derive(Debug, Clone)]
pub struct TaskRepository {
    pool: SqlitePool,
}

impl TaskRepository {
    /// Create a new task repository
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// Create a new task
    pub async fn create(&self, new_task: &NewTask) -> Result<Task> {
        debug!("Creating task: {}", new_task.name);

        let task = sqlx::query_as::<_, Task>(
            r#"
            INSERT INTO tasks (name, target, scan_type, config, status, progress, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'pending', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING *
            "#
        )
        .bind(&new_task.name)
        .bind(&new_task.target)
        .bind(&new_task.scan_type)
        .bind(&new_task.config)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create task: {}", e)))?;

        info!("Created task with ID: {}", task.id);
        Ok(task)
    }

    /// Get a task by ID
    pub async fn get_by_id(&self, id: i64) -> Result<Option<Task>> {
        debug!("Getting task by ID: {}", id);

        let task = sqlx::query_as::<_, Task>("SELECT * FROM tasks WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get task: {}", e)))?;

        Ok(task)
    }

    /// List tasks with optional filters
    pub async fn list(&self, limit: Option<i64>, offset: Option<i64>) -> Result<Vec<TaskSummary>> {
        debug!(
            "Listing tasks with limit: {:?}, offset: {:?}",
            limit, offset
        );

        let mut query = "SELECT id, name, target, scan_type, status, progress, created_at, updated_at FROM tasks ORDER BY created_at DESC".to_string();

        if let Some(limit) = limit {
            query.push_str(&format!(" LIMIT {}", limit));
            if let Some(offset) = offset {
                query.push_str(&format!(" OFFSET {}", offset));
            }
        }

        let tasks = sqlx::query_as::<_, TaskSummary>(&query)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to list tasks: {}", e)))?;

        debug!("Found {} tasks", tasks.len());
        Ok(tasks)
    }

    /// Update task status
    pub async fn update_status(&self, id: i64, status: &str) -> Result<bool> {
        debug!("Updating task {} status to: {}", id, status);

        let result =
            sqlx::query("UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
                .bind(status)
                .bind(id)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::database(format!("Failed to update task status: {}", e)))?;

        Ok(result.rows_affected() > 0)
    }

    /// Update task progress
    pub async fn update_progress(&self, id: i64, progress: i32) -> Result<bool> {
        debug!("Updating task {} progress to: {}%", id, progress);

        let result = sqlx::query(
            "UPDATE tasks SET progress = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        )
        .bind(progress)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update task progress: {}", e)))?;

        Ok(result.rows_affected() > 0)
    }

    /// Delete a task
    pub async fn delete(&self, id: i64) -> Result<bool> {
        info!("Deleting task with ID: {}", id);

        let result = sqlx::query("DELETE FROM tasks WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to delete task: {}", e)))?;

        let deleted = result.rows_affected() > 0;
        if deleted {
            info!("Task {} deleted successfully", id);
        }

        Ok(deleted)
    }

    /// Get tasks by status
    pub async fn get_by_status(&self, status: &str) -> Result<Vec<TaskSummary>> {
        debug!("Getting tasks by status: {}", status);

        let tasks = sqlx::query_as::<_, TaskSummary>(
            "SELECT id, name, target, scan_type, status, progress, created_at, updated_at FROM tasks WHERE status = ? ORDER BY created_at DESC"
        )
        .bind(status)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get tasks by status: {}", e)))?;

        Ok(tasks)
    }

    /// Count tasks by status
    pub async fn count_by_status(&self, status: &str) -> Result<i64> {
        debug!("Counting tasks by status: {}", status);

        let row = sqlx::query("SELECT COUNT(*) as count FROM tasks WHERE status = ?")
            .bind(status)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count tasks: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count)
    }

    /// Get total task count
    pub async fn count_total(&self) -> Result<i64> {
        debug!("Counting total tasks");

        let row = sqlx::query("SELECT COUNT(*) as count FROM tasks")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count total tasks: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    async fn create_test_repository() -> TaskRepository {
        let temp_file = NamedTempFile::new().unwrap();
        let db_url = format!("sqlite:{}", temp_file.path().display());
        let pool = crate::database::get_database_pool(&db_url).await.unwrap();
        crate::database::initialize_database(&pool).await.unwrap();
        TaskRepository::new(pool)
    }

    #[tokio::test]
    async fn test_create_and_get_task() {
        let repo = create_test_repository().await;

        let new_task = NewTask::new(
            "Test Task".to_string(),
            "127.0.0.1".to_string(),
            "standard".to_string(),
            "{}".to_string(),
        );

        let task = repo.create(&new_task).await.unwrap();
        assert_eq!(task.name, "Test Task");
        assert_eq!(task.status, "pending");

        let retrieved = repo.get_by_id(task.id).await.unwrap().unwrap();
        assert_eq!(retrieved.id, task.id);
        assert_eq!(retrieved.name, task.name);
    }

    #[tokio::test]
    async fn test_update_status() {
        let repo = create_test_repository().await;

        let new_task = NewTask::new(
            "Status Test".to_string(),
            "127.0.0.1".to_string(),
            "quick".to_string(),
            "{}".to_string(),
        );

        let task = repo.create(&new_task).await.unwrap();

        let updated = repo.update_status(task.id, "running").await.unwrap();
        assert!(updated);

        let retrieved = repo.get_by_id(task.id).await.unwrap().unwrap();
        assert_eq!(retrieved.status, "running");
    }

    #[tokio::test]
    async fn test_list_tasks() {
        let repo = create_test_repository().await;

        // Create multiple tasks
        for i in 1..=3 {
            let new_task = NewTask::new(
                format!("Task {}", i),
                "127.0.0.1".to_string(),
                "standard".to_string(),
                "{}".to_string(),
            );
            repo.create(&new_task).await.unwrap();
        }

        let tasks = repo.list(None, None).await.unwrap();
        assert_eq!(tasks.len(), 3);

        let limited_tasks = repo.list(Some(2), None).await.unwrap();
        assert_eq!(limited_tasks.len(), 2);
    }
}

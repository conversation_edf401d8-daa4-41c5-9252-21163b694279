//! Configuration repository implementation

use sqlx::{Row, SqlitePool};
use tracing::{debug, info};

use crate::common::error::{AppError, Result};
use crate::database::models::{NewSystemConfig, SystemConfig};

#[derive(Debug, Clone)]
pub struct ConfigRepository {
    pool: SqlitePool,
}

impl ConfigRepository {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn get(&self, key: &str) -> Result<Option<SystemConfig>> {
        debug!("Getting config for key: {}", key);

        let config = sqlx::query_as::<_, SystemConfig>("SELECT * FROM system_config WHERE key = ?")
            .bind(key)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get config: {}", e)))?;

        Ok(config)
    }

    pub async fn set(&self, config: &NewSystemConfig) -> Result<SystemConfig> {
        debug!("Setting config for key: {}", config.key);

        let result = sqlx::query_as::<_, SystemConfig>(
            r#"
            INSERT INTO system_config (key, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ON CONFLICT(key) DO UPDATE SET
                value = excluded.value,
                description = excluded.description,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
            "#,
        )
        .bind(&config.key)
        .bind(&config.value)
        .bind(&config.description)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to set config: {}", e)))?;

        Ok(result)
    }

    pub async fn delete(&self, key: &str) -> Result<bool> {
        info!("Deleting config for key: {}", key);

        let result = sqlx::query("DELETE FROM system_config WHERE key = ?")
            .bind(key)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to delete config: {}", e)))?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn list_all(&self) -> Result<Vec<SystemConfig>> {
        debug!("Listing all config entries");

        let configs = sqlx::query_as::<_, SystemConfig>("SELECT * FROM system_config ORDER BY key")
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to list configs: {}", e)))?;

        Ok(configs)
    }
}

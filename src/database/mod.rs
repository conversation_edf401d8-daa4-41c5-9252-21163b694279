//! Database layer
//!
//! This module provides database connectivity, models, and repositories
//! for persistent storage of scanning tasks and results.

pub mod connection;
pub mod models;
pub mod repositories;

// Re-export commonly used types
pub use connection::{DatabasePool, get_database_pool};
pub use models::{Task, ScanResult, SystemConfig};
pub use repositories::{TaskRepository, ResultRepository, ConfigRepository};

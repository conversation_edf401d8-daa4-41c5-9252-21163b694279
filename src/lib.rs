//! RustScan Web - Modern Web Security Scanning Platform
//!
//! This crate provides both the core scanning functionality from the original RustScan
//! and a modern web interface for security scanning operations.
//!
//! ## Core Scanning Example
//!
//! ```rust
//! use rustscan_web::core::{Scanner, PortStrategy, ScanOrder, PortRange};
//! use std::{net::IpAddr, time::Duration};
//! use tokio;
//!
//! #[tokio::main]
//! async fn main() {
//!     let addrs = vec!["127.0.0.1".parse::<IpAddr>().unwrap()];
//!     let range = PortRange { start: 1, end: 1000 };
//!     let strategy = PortStrategy::pick(&Some(range), None, ScanOrder::Serial);
//!
//!     let scanner = Scanner::new(
//!         &addrs,
//!         100,                              // batch_size
//!         Duration::from_millis(1000),      // timeout
//!         1,                                // tries
//!         false,                            // greppable
//!         strategy,                         // port strategy
//!         true,                             // accessible
//!         vec![],                           // exclude_ports
//!         false,                            // udp
//!     );
//!
//!     let results = scanner.run().await;
//!     println!("Found {} open ports", results.len());
//! }
//! ```
//!
//! ## Web Service Example
//!
//! ```rust,no_run
//! use rustscan_web::web::server::create_app;
//! use actix_web::HttpServer;
//!
//! #[tokio::main]
//! async fn main() -> std::io::Result<()> {
//!     let app = create_app().await;
//!
//!     HttpServer::new(move || app.clone())
//!         .bind("127.0.0.1:8080")?
//!         .run()
//!         .await
//! }
//! ```

#![deny(clippy::all)]
#![warn(clippy::pedantic)]
#![allow(clippy::doc_markdown, clippy::if_not_else, clippy::non_ascii_literal)]

// Core scanning modules (migrated from original RustScan)
pub mod core;

// Web application modules
#[cfg(feature = "web")]
pub mod web;

// Business logic services
pub mod services;

// External tool integrations
pub mod tools;

// Database layer
#[cfg(feature = "web")]
pub mod database;

// Common utilities and types
pub mod common;

// Legacy modules for backward compatibility
pub mod address;
pub mod benchmark;
pub mod generated;
pub mod input;
pub mod port_strategy;
pub mod scanner;
pub mod scripts;
pub mod tui;

//! Notification service
//!
//! This service handles real-time notifications via WebSocket and other channels.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::common::error::{AppError, Result};
use crate::web::models::WebSocketMessage;

/// Notification service for real-time updates
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct NotificationService {
    subscribers: Arc<RwLock<HashMap<String, NotificationSubscriber>>>,
}

/// WebSocket subscriber information
#[derive(Debug, <PERSON><PERSON>)]
pub struct NotificationSubscriber {
    pub id: String,
    pub sender: tokio::sync::mpsc::UnboundedSender<WebSocketMessage>,
    pub subscribed_tasks: Vec<i64>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
}

impl NotificationService {
    /// Create a new notification service
    pub fn new() -> Self {
        Self {
            subscribers: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Subscribe to notifications
    pub async fn subscribe(
        &self,
        subscriber_id: String,
        sender: tokio::sync::mpsc::UnboundedSender<WebSocketMessage>,
    ) -> Result<()> {
        info!("New subscriber: {}", subscriber_id);

        let subscriber = NotificationSubscriber {
            id: subscriber_id.clone(),
            sender,
            subscribed_tasks: Vec::new(),
            connected_at: chrono::Utc::now(),
        };

        let mut subscribers = self.subscribers.write().await;
        subscribers.insert(subscriber_id, subscriber);

        Ok(())
    }

    /// Unsubscribe from notifications
    pub async fn unsubscribe(&self, subscriber_id: &str) -> Result<()> {
        info!("Unsubscribing: {}", subscriber_id);

        let mut subscribers = self.subscribers.write().await;
        subscribers.remove(subscriber_id);

        Ok(())
    }

    /// Subscribe to task updates
    pub async fn subscribe_to_task(&self, subscriber_id: &str, task_id: i64) -> Result<()> {
        debug!(
            "Subscriber {} subscribing to task {}",
            subscriber_id, task_id
        );

        let mut subscribers = self.subscribers.write().await;
        if let Some(subscriber) = subscribers.get_mut(subscriber_id) {
            if !subscriber.subscribed_tasks.contains(&task_id) {
                subscriber.subscribed_tasks.push(task_id);
            }
            Ok(())
        } else {
            Err(AppError::not_found("Subscriber not found"))
        }
    }

    /// Unsubscribe from task updates
    pub async fn unsubscribe_from_task(&self, subscriber_id: &str, task_id: i64) -> Result<()> {
        debug!(
            "Subscriber {} unsubscribing from task {}",
            subscriber_id, task_id
        );

        let mut subscribers = self.subscribers.write().await;
        if let Some(subscriber) = subscribers.get_mut(subscriber_id) {
            subscriber.subscribed_tasks.retain(|&id| id != task_id);
            Ok(())
        } else {
            Err(AppError::not_found("Subscriber not found"))
        }
    }

    /// Send task status update
    pub async fn notify_task_status(
        &self,
        task_id: i64,
        status: String,
        progress: i32,
        message: Option<String>,
    ) {
        let message = WebSocketMessage::TaskStatusUpdate {
            task_id,
            status,
            progress,
            message,
        };

        self.broadcast_to_task_subscribers(task_id, message).await;
    }

    /// Send task progress update
    pub async fn notify_task_progress(
        &self,
        task_id: i64,
        progress: i32,
        current_target: Option<String>,
        found_ports: Option<Vec<u16>>,
    ) {
        let message = WebSocketMessage::TaskProgress {
            task_id,
            progress,
            current_target,
            found_ports,
        };

        self.broadcast_to_task_subscribers(task_id, message).await;
    }

    /// Send task completion notification
    pub async fn notify_task_completed(&self, task_id: i64, duration: i64, total_results: i64) {
        let message = WebSocketMessage::TaskCompleted {
            task_id,
            duration,
            total_results,
        };

        self.broadcast_to_task_subscribers(task_id, message).await;
    }

    /// Send task error notification
    pub async fn notify_task_error(&self, task_id: i64, error: String) {
        let message = WebSocketMessage::TaskError { task_id, error };

        self.broadcast_to_task_subscribers(task_id, message).await;
    }

    /// Send system notification
    pub async fn notify_system(&self, level: String, message: String) {
        let notification = WebSocketMessage::SystemNotification { level, message };

        self.broadcast_to_all(notification).await;
    }

    /// Broadcast message to all task subscribers
    async fn broadcast_to_task_subscribers(&self, task_id: i64, message: WebSocketMessage) {
        let subscribers = self.subscribers.read().await;
        let mut failed_subscribers = Vec::new();

        for (subscriber_id, subscriber) in subscribers.iter() {
            if subscriber.subscribed_tasks.contains(&task_id) {
                if let Err(_) = subscriber.sender.send(message.clone()) {
                    warn!("Failed to send message to subscriber {}", subscriber_id);
                    failed_subscribers.push(subscriber_id.clone());
                }
            }
        }

        // Clean up failed subscribers
        drop(subscribers);
        if !failed_subscribers.is_empty() {
            let mut subscribers = self.subscribers.write().await;
            for subscriber_id in failed_subscribers {
                subscribers.remove(&subscriber_id);
            }
        }
    }

    /// Broadcast message to all subscribers
    async fn broadcast_to_all(&self, message: WebSocketMessage) {
        let subscribers = self.subscribers.read().await;
        let mut failed_subscribers = Vec::new();

        for (subscriber_id, subscriber) in subscribers.iter() {
            if let Err(_) = subscriber.sender.send(message.clone()) {
                warn!("Failed to send message to subscriber {}", subscriber_id);
                failed_subscribers.push(subscriber_id.clone());
            }
        }

        // Clean up failed subscribers
        drop(subscribers);
        if !failed_subscribers.is_empty() {
            let mut subscribers = self.subscribers.write().await;
            for subscriber_id in failed_subscribers {
                subscribers.remove(&subscriber_id);
            }
        }
    }

    /// Get subscriber count
    pub async fn get_subscriber_count(&self) -> usize {
        let subscribers = self.subscribers.read().await;
        subscribers.len()
    }

    /// Get active subscribers info
    pub async fn get_subscribers_info(&self) -> Vec<SubscriberInfo> {
        let subscribers = self.subscribers.read().await;
        subscribers
            .values()
            .map(|s| SubscriberInfo {
                id: s.id.clone(),
                subscribed_tasks: s.subscribed_tasks.clone(),
                connected_at: s.connected_at,
                connection_duration: chrono::Utc::now() - s.connected_at,
            })
            .collect()
    }

    /// Clean up stale connections
    pub async fn cleanup_stale_connections(&self, max_age: chrono::Duration) {
        let cutoff_time = chrono::Utc::now() - max_age;
        let mut subscribers = self.subscribers.write().await;

        let stale_ids: Vec<String> = subscribers
            .iter()
            .filter(|(_, subscriber)| subscriber.connected_at < cutoff_time)
            .map(|(id, _)| id.clone())
            .collect();

        for id in stale_ids {
            subscribers.remove(&id);
            info!("Removed stale subscriber: {}", id);
        }
    }
}

impl Default for NotificationService {
    fn default() -> Self {
        Self::new()
    }
}

/// Subscriber information for monitoring
#[derive(Debug, Clone, serde::Serialize)]
pub struct SubscriberInfo {
    pub id: String,
    pub subscribed_tasks: Vec<i64>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub connection_duration: chrono::Duration,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_notification_service() {
        let service = NotificationService::new();

        // Create a mock subscriber
        let (tx, mut rx) = mpsc::unbounded_channel();
        let subscriber_id = "test-subscriber".to_string();

        service.subscribe(subscriber_id.clone(), tx).await.unwrap();

        // Subscribe to a task
        service.subscribe_to_task(&subscriber_id, 1).await.unwrap();

        // Send a notification
        service
            .notify_task_status(1, "running".to_string(), 50, Some("Testing".to_string()))
            .await;

        // Check if message was received
        let message = rx.recv().await.unwrap();
        match message {
            WebSocketMessage::TaskStatusUpdate {
                task_id,
                status,
                progress,
                ..
            } => {
                assert_eq!(task_id, 1);
                assert_eq!(status, "running");
                assert_eq!(progress, 50);
            }
            _ => panic!("Wrong message type"),
        }

        // Unsubscribe
        service.unsubscribe(&subscriber_id).await.unwrap();
        assert_eq!(service.get_subscriber_count().await, 0);
    }

    #[tokio::test]
    async fn test_multiple_subscribers() {
        let service = NotificationService::new();

        // Create multiple subscribers
        let (tx1, mut rx1) = mpsc::unbounded_channel();
        let (tx2, mut rx2) = mpsc::unbounded_channel();

        service.subscribe("sub1".to_string(), tx1).await.unwrap();
        service.subscribe("sub2".to_string(), tx2).await.unwrap();

        // Both subscribe to the same task
        service.subscribe_to_task("sub1", 1).await.unwrap();
        service.subscribe_to_task("sub2", 1).await.unwrap();

        // Send notification
        service
            .notify_task_progress(1, 75, Some("127.0.0.1".to_string()), Some(vec![80, 443]))
            .await;

        // Both should receive the message
        let msg1 = rx1.recv().await.unwrap();
        let msg2 = rx2.recv().await.unwrap();

        match (msg1, msg2) {
            (
                WebSocketMessage::TaskProgress {
                    task_id: id1,
                    progress: p1,
                    ..
                },
                WebSocketMessage::TaskProgress {
                    task_id: id2,
                    progress: p2,
                    ..
                },
            ) => {
                assert_eq!(id1, 1);
                assert_eq!(id2, 1);
                assert_eq!(p1, 75);
                assert_eq!(p2, 75);
            }
            _ => panic!("Wrong message types"),
        }
    }

    #[tokio::test]
    async fn test_system_notifications() {
        let service = NotificationService::new();

        let (tx, mut rx) = mpsc::unbounded_channel();
        service.subscribe("test".to_string(), tx).await.unwrap();

        // Send system notification
        service
            .notify_system("info".to_string(), "System started".to_string())
            .await;

        let message = rx.recv().await.unwrap();
        match message {
            WebSocketMessage::SystemNotification { level, message } => {
                assert_eq!(level, "info");
                assert_eq!(message, "System started");
            }
            _ => panic!("Wrong message type"),
        }
    }
}

//! Scan execution service
//!
//! This service handles the execution of scanning tasks.

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

use crate::common::error::{AppError, Result};
use crate::core::{ScanConfig, Scanner};

/// Scan execution service
#[derive(Debug, <PERSON>lone)]
pub struct ScanService {
    active_scans: Arc<RwLock<HashMap<i64, ScanHandle>>>,
}

/// Handle for an active scan
#[derive(Debug)]
pub struct ScanHandle {
    pub task_id: i64,
    pub cancel_token: tokio_util::sync::CancellationToken,
    pub started_at: chrono::DateTime<chrono::Utc>,
}

impl ScanService {
    /// Create a new scan service
    pub fn new() -> Self {
        Self {
            active_scans: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Start a scan for the given task
    pub async fn start_scan(&self, task_id: i64, config: ScanConfig) -> Result<()> {
        info!("Starting scan for task {}", task_id);

        // Check if scan is already running
        {
            let active_scans = self.active_scans.read().await;
            if active_scans.contains_key(&task_id) {
                return Err(AppError::validation(
                    "Scan is already running for this task",
                ));
            }
        }

        // Create cancellation token
        let cancel_token = tokio_util::sync::CancellationToken::new();
        let cancel_token_clone = cancel_token.clone();

        // Create scan handle
        let handle = ScanHandle {
            task_id,
            cancel_token,
            started_at: chrono::Utc::now(),
        };

        // Add to active scans
        {
            let mut active_scans = self.active_scans.write().await;
            active_scans.insert(task_id, handle);
        }

        // Start scan in background
        let service_clone = self.clone();
        tokio::spawn(async move {
            let result = service_clone
                .execute_scan(task_id, config, cancel_token_clone)
                .await;

            // Remove from active scans
            {
                let mut active_scans = service_clone.active_scans.write().await;
                active_scans.remove(&task_id);
            }

            match result {
                Ok(_) => info!("Scan completed successfully for task {}", task_id),
                Err(e) => error!("Scan failed for task {}: {}", task_id, e),
            }
        });

        Ok(())
    }

    /// Stop a running scan
    pub async fn stop_scan(&self, task_id: i64) -> Result<bool> {
        info!("Stopping scan for task {}", task_id);

        let mut active_scans = self.active_scans.write().await;
        if let Some(handle) = active_scans.remove(&task_id) {
            handle.cancel_token.cancel();
            info!("Scan stopped for task {}", task_id);
            Ok(true)
        } else {
            warn!("No active scan found for task {}", task_id);
            Ok(false)
        }
    }

    /// Check if a scan is running for the given task
    pub async fn is_scan_running(&self, task_id: i64) -> bool {
        let active_scans = self.active_scans.read().await;
        active_scans.contains_key(&task_id)
    }

    /// Get list of active scan task IDs
    pub async fn get_active_scans(&self) -> Vec<i64> {
        let active_scans = self.active_scans.read().await;
        active_scans.keys().copied().collect()
    }

    /// Get scan statistics
    pub async fn get_scan_stats(&self) -> ScanStats {
        let active_scans = self.active_scans.read().await;
        ScanStats {
            active_scans: active_scans.len(),
            total_scans_started: 0, // Would need persistent storage for this
        }
    }

    /// Execute the actual scan
    async fn execute_scan(
        &self,
        task_id: i64,
        config: ScanConfig,
        cancel_token: tokio_util::sync::CancellationToken,
    ) -> Result<()> {
        debug!(
            "Executing scan for task {} with config: {:?}",
            task_id, config
        );

        // Create scanner
        let scanner = Scanner::from_config(&config).await?;

        // Check for cancellation before starting
        if cancel_token.is_cancelled() {
            return Err(AppError::internal_error(
                "Scan was cancelled before starting",
            ));
        }

        // Run scan with cancellation support
        let scan_future = scanner.run();
        let scan_result = tokio::select! {
            result = scan_future => result,
            _ = cancel_token.cancelled() => {
                warn!("Scan cancelled for task {}", task_id);
                return Err(AppError::internal_error("Scan was cancelled"));
            }
        };

        debug!(
            "Scan completed for task {}, found {} open ports",
            task_id,
            scan_result.len()
        );

        // TODO: Store scan results in database
        // This would involve:
        // 1. Converting scan_result to database format
        // 2. Storing results in scan_results table
        // 3. Updating task status and progress

        Ok(())
    }
}

impl Default for ScanService {
    fn default() -> Self {
        Self::new()
    }
}

/// Scan statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ScanStats {
    pub active_scans: usize,
    pub total_scans_started: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::config::{PortRange, ScanConfig};

    #[tokio::test]
    async fn test_scan_service_creation() {
        let service = ScanService::new();
        assert_eq!(service.get_active_scans().await.len(), 0);
    }

    #[tokio::test]
    async fn test_scan_lifecycle() {
        let service = ScanService::new();
        let task_id = 1;

        // Initially no active scans
        assert!(!service.is_scan_running(task_id).await);

        // Create a simple config for testing
        let mut config = ScanConfig::new(vec!["127.0.0.1".to_string()]);
        config.range = Some(PortRange { start: 80, end: 80 });

        // Start scan
        service.start_scan(task_id, config).await.unwrap();

        // Should now be running
        assert!(service.is_scan_running(task_id).await);

        // Stop scan
        let stopped = service.stop_scan(task_id).await.unwrap();
        assert!(stopped);

        // Give it a moment to clean up
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;

        // Should no longer be running
        assert!(!service.is_scan_running(task_id).await);
    }

    #[tokio::test]
    async fn test_multiple_scans() {
        let service = ScanService::new();

        let mut config1 = ScanConfig::new(vec!["127.0.0.1".to_string()]);
        config1.range = Some(PortRange { start: 80, end: 80 });

        let mut config2 = ScanConfig::new(vec!["*********".to_string()]);
        config2.range = Some(PortRange {
            start: 443,
            end: 443,
        });

        // Start multiple scans
        service.start_scan(1, config1).await.unwrap();
        service.start_scan(2, config2).await.unwrap();

        // Both should be running
        assert!(service.is_scan_running(1).await);
        assert!(service.is_scan_running(2).await);

        let active = service.get_active_scans().await;
        assert_eq!(active.len(), 2);
        assert!(active.contains(&1));
        assert!(active.contains(&2));

        // Stop all scans
        service.stop_scan(1).await.unwrap();
        service.stop_scan(2).await.unwrap();
    }
}

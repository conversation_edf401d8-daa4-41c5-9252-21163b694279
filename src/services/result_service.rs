//! Result management service
//!
//! This service handles storage and retrieval of scan results.

use std::sync::Arc;
use tracing::{info, debug};

use crate::database::DatabasePool;
use crate::common::error::{AppError, Result};

/// Result management service
#[derive(Debug, Clone)]
pub struct ResultService {
    db_pool: Arc<DatabasePool>,
}

impl ResultService {
    /// Create a new result service
    pub fn new(db_pool: DatabasePool) -> Self {
        Self {
            db_pool: Arc::new(db_pool),
        }
    }
    
    /// Store scan results
    pub async fn store_results(&self, task_id: i64, results: Vec<ScanResultData>) -> Result<()> {
        info!("Storing {} results for task {}", results.len(), task_id);
        
        let mut tx = self.db_pool.begin().await
            .map_err(|e| AppError::database(format!("Failed to start transaction: {}", e)))?;
        
        for result in results {
            sqlx::query(
                "INSERT INTO scan_results (task_id, tool, target, result_type, data) VALUES (?, ?, ?, ?, ?)"
            )
            .bind(task_id)
            .bind(&result.tool)
            .bind(&result.target)
            .bind(&result.result_type)
            .bind(&result.data)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::database(format!("Failed to store result: {}", e)))?;
        }
        
        tx.commit().await
            .map_err(|e| AppError::database(format!("Failed to commit results: {}", e)))?;
        
        info!("Successfully stored {} results for task {}", results.len(), task_id);
        Ok(())
    }
    
    /// Get results for a task
    pub async fn get_task_results(&self, task_id: i64) -> Result<Vec<ScanResultData>> {
        debug!("Getting results for task {}", task_id);
        
        let results = sqlx::query_as::<_, ScanResultData>(
            "SELECT tool, target, result_type, data FROM scan_results WHERE task_id = ? ORDER BY created_at"
        )
        .bind(task_id)
        .fetch_all(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get results: {}", e)))?;
        
        debug!("Found {} results for task {}", results.len(), task_id);
        Ok(results)
    }
    
    /// Get results by type
    pub async fn get_results_by_type(&self, task_id: i64, result_type: &str) -> Result<Vec<ScanResultData>> {
        debug!("Getting {} results for task {}", result_type, task_id);
        
        let results = sqlx::query_as::<_, ScanResultData>(
            "SELECT tool, target, result_type, data FROM scan_results WHERE task_id = ? AND result_type = ? ORDER BY created_at"
        )
        .bind(task_id)
        .bind(result_type)
        .fetch_all(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get results by type: {}", e)))?;
        
        Ok(results)
    }
    
    /// Delete results for a task
    pub async fn delete_task_results(&self, task_id: i64) -> Result<u64> {
        info!("Deleting results for task {}", task_id);
        
        let result = sqlx::query("DELETE FROM scan_results WHERE task_id = ?")
            .bind(task_id)
            .execute(&**self.db_pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to delete results: {}", e)))?;
        
        let deleted_count = result.rows_affected();
        info!("Deleted {} results for task {}", deleted_count, task_id);
        Ok(deleted_count)
    }
    
    /// Get result statistics
    pub async fn get_result_stats(&self, task_id: i64) -> Result<ResultStats> {
        debug!("Getting result statistics for task {}", task_id);
        
        let stats = sqlx::query_as::<_, ResultStats>(
            "SELECT 
                COUNT(*) as total_results,
                COUNT(DISTINCT tool) as unique_tools,
                COUNT(DISTINCT target) as unique_targets,
                COUNT(DISTINCT result_type) as unique_types
             FROM scan_results WHERE task_id = ?"
        )
        .bind(task_id)
        .fetch_one(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get result stats: {}", e)))?;
        
        Ok(stats)
    }
    
    /// Export results in various formats
    pub async fn export_results(&self, task_id: i64, format: ExportFormat) -> Result<String> {
        let results = self.get_task_results(task_id).await?;
        
        match format {
            ExportFormat::Json => {
                serde_json::to_string_pretty(&results)
                    .map_err(|e| AppError::internal_error(&format!("Failed to serialize to JSON: {}", e)))
            }
            ExportFormat::Csv => {
                self.export_to_csv(results)
            }
            ExportFormat::Xml => {
                self.export_to_xml(results)
            }
        }
    }
    
    /// Export results to CSV format
    fn export_to_csv(&self, results: Vec<ScanResultData>) -> Result<String> {
        let mut csv = String::from("tool,target,result_type,data\n");
        
        for result in results {
            let data_str = serde_json::to_string(&result.data)
                .map_err(|e| AppError::internal_error(&format!("Failed to serialize data: {}", e)))?;
            
            csv.push_str(&format!("{},{},{},\"{}\"\n", 
                result.tool, result.target, result.result_type, 
                data_str.replace('"', "\"\"")
            ));
        }
        
        Ok(csv)
    }
    
    /// Export results to XML format
    fn export_to_xml(&self, results: Vec<ScanResultData>) -> Result<String> {
        let mut xml = String::from("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<scan_results>\n");
        
        for result in results {
            xml.push_str(&format!(
                "  <result>\n    <tool>{}</tool>\n    <target>{}</target>\n    <type>{}</type>\n    <data>{}</data>\n  </result>\n",
                result.tool, result.target, result.result_type,
                serde_json::to_string(&result.data).unwrap_or_default()
            ));
        }
        
        xml.push_str("</scan_results>");
        Ok(xml)
    }
}

/// Scan result data structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize, sqlx::FromRow)]
pub struct ScanResultData {
    pub tool: String,
    pub target: String,
    pub result_type: String,
    pub data: serde_json::Value,
}

/// Result statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize, sqlx::FromRow)]
pub struct ResultStats {
    pub total_results: i64,
    pub unique_tools: i64,
    pub unique_targets: i64,
    pub unique_types: i64,
}

/// Export format options
#[derive(Debug, Clone, Copy)]
pub enum ExportFormat {
    Json,
    Csv,
    Xml,
}

impl std::str::FromStr for ExportFormat {
    type Err = String;
    
    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "json" => Ok(ExportFormat::Json),
            "csv" => Ok(ExportFormat::Csv),
            "xml" => Ok(ExportFormat::Xml),
            _ => Err(format!("Invalid export format: {}", s)),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    async fn create_test_service() -> ResultService {
        let temp_file = NamedTempFile::new().unwrap();
        let db_url = format!("sqlite:{}", temp_file.path().display());
        let pool = crate::database::get_database_pool(&db_url).await.unwrap();
        crate::database::initialize_database(&pool).await.unwrap();
        ResultService::new(pool)
    }

    #[tokio::test]
    async fn test_store_and_retrieve_results() {
        let service = create_test_service().await;
        let task_id = 1;
        
        let results = vec![
            ScanResultData {
                tool: "rustscan".to_string(),
                target: "127.0.0.1".to_string(),
                result_type: "open_port".to_string(),
                data: serde_json::json!({"port": 80, "service": "http"}),
            },
            ScanResultData {
                tool: "rustscan".to_string(),
                target: "127.0.0.1".to_string(),
                result_type: "open_port".to_string(),
                data: serde_json::json!({"port": 443, "service": "https"}),
            },
        ];
        
        service.store_results(task_id, results.clone()).await.unwrap();
        
        let retrieved = service.get_task_results(task_id).await.unwrap();
        assert_eq!(retrieved.len(), 2);
        assert_eq!(retrieved[0].tool, "rustscan");
        assert_eq!(retrieved[0].target, "127.0.0.1");
    }

    #[tokio::test]
    async fn test_result_stats() {
        let service = create_test_service().await;
        let task_id = 1;
        
        let results = vec![
            ScanResultData {
                tool: "rustscan".to_string(),
                target: "127.0.0.1".to_string(),
                result_type: "open_port".to_string(),
                data: serde_json::json!({"port": 80}),
            },
            ScanResultData {
                tool: "nmap".to_string(),
                target: "127.0.0.2".to_string(),
                result_type: "service".to_string(),
                data: serde_json::json!({"service": "http"}),
            },
        ];
        
        service.store_results(task_id, results).await.unwrap();
        
        let stats = service.get_result_stats(task_id).await.unwrap();
        assert_eq!(stats.total_results, 2);
        assert_eq!(stats.unique_tools, 2);
        assert_eq!(stats.unique_targets, 2);
        assert_eq!(stats.unique_types, 2);
    }

    #[test]
    fn test_export_format_parsing() {
        assert!(matches!("json".parse::<ExportFormat>().unwrap(), ExportFormat::Json));
        assert!(matches!("CSV".parse::<ExportFormat>().unwrap(), ExportFormat::Csv));
        assert!(matches!("xml".parse::<ExportFormat>().unwrap(), ExportFormat::Xml));
        assert!("invalid".parse::<ExportFormat>().is_err());
    }
}

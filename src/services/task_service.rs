//! Task management service
//!
//! This service handles CRUD operations for scanning tasks.

use std::sync::Arc;
use tracing::{info, warn, debug};

use crate::database::{DatabasePool, models::{Task, NewTask, TaskUpdate, TaskSummary}};
use crate::common::error::{AppError, Result};
use crate::web::routes::tasks::{TaskListQuery, CreateTaskRequest, UpdateTaskRequest};

/// Task management service
#[derive(Debug, Clone)]
pub struct TaskService {
    db_pool: Arc<DatabasePool>,
}

impl TaskService {
    /// Create a new task service
    pub fn new(db_pool: DatabasePool) -> Self {
        Self {
            db_pool: Arc::new(db_pool),
        }
    }
    
    /// List tasks with optional filtering
    pub async fn list_tasks(&self, query: &TaskListQuery) -> Result<Vec<TaskSummary>> {
        debug!("Listing tasks with query: {:?}", query);
        
        let mut sql = "SELECT id, name, target, scan_type, status, progress, created_at, updated_at FROM tasks".to_string();
        let mut conditions = Vec::new();
        
        // Add filters
        if let Some(ref status) = query.status {
            conditions.push(format!("status = '{}'", status));
        }
        
        if let Some(ref scan_type) = query.scan_type {
            conditions.push(format!("scan_type = '{}'", scan_type));
        }
        
        if !conditions.is_empty() {
            sql.push_str(&format!(" WHERE {}", conditions.join(" AND ")));
        }
        
        // Add sorting
        let sort_by = query.sort_by.as_deref().unwrap_or("created_at");
        let sort_order = query.sort_order.as_deref().unwrap_or("DESC");
        sql.push_str(&format!(" ORDER BY {} {}", sort_by, sort_order));
        
        // Add pagination
        if let Some(limit) = query.limit {
            sql.push_str(&format!(" LIMIT {}", limit));
            
            if let Some(offset) = query.offset {
                sql.push_str(&format!(" OFFSET {}", offset));
            }
        }
        
        let tasks = sqlx::query_as::<_, TaskSummary>(&sql)
            .fetch_all(&**self.db_pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to list tasks: {}", e)))?;
        
        debug!("Found {} tasks", tasks.len());
        Ok(tasks)
    }
    
    /// Create a new task
    pub async fn create_task(&self, request: &CreateTaskRequest) -> Result<Task> {
        info!("Creating task: {}", request.name);
        
        let config_json = serde_json::to_string(&request.config)
            .map_err(|e| AppError::validation(format!("Invalid config: {}", e)))?;
        
        let new_task = NewTask::new(
            request.name.clone(),
            request.target.clone(),
            request.scan_type.clone(),
            config_json,
        );
        
        new_task.validate()
            .map_err(|e| AppError::validation(e))?;
        
        let task = sqlx::query_as::<_, Task>(
            "INSERT INTO tasks (name, target, scan_type, config) VALUES (?, ?, ?, ?) RETURNING *"
        )
        .bind(&new_task.name)
        .bind(&new_task.target)
        .bind(&new_task.scan_type)
        .bind(&new_task.config)
        .fetch_one(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create task: {}", e)))?;
        
        info!("Task created with ID: {}", task.id);
        Ok(task)
    }
    
    /// Get a task by ID
    pub async fn get_task(&self, task_id: i64) -> Result<Option<Task>> {
        debug!("Getting task with ID: {}", task_id);
        
        let task = sqlx::query_as::<_, Task>("SELECT * FROM tasks WHERE id = ?")
            .bind(task_id)
            .fetch_optional(&**self.db_pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get task: {}", e)))?;
        
        Ok(task)
    }
    
    /// Update a task
    pub async fn update_task(&self, task_id: i64, request: &UpdateTaskRequest) -> Result<Option<Task>> {
        debug!("Updating task with ID: {}", task_id);
        
        let mut updates = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = Vec::new();
        
        if let Some(ref name) = request.name {
            updates.push("name = ?");
            params.push(Box::new(name.clone()));
        }
        
        if let Some(ref config) = request.config {
            let config_json = serde_json::to_string(config)
                .map_err(|e| AppError::validation(format!("Invalid config: {}", e)))?;
            updates.push("config = ?");
            params.push(Box::new(config_json));
        }
        
        if updates.is_empty() {
            return self.get_task(task_id).await;
        }
        
        updates.push("updated_at = CURRENT_TIMESTAMP");
        
        let sql = format!("UPDATE tasks SET {} WHERE id = ? RETURNING *", updates.join(", "));
        params.push(Box::new(task_id));
        
        // For now, return a simple implementation
        // In a real implementation, you'd need to handle dynamic parameters properly
        let task = self.get_task(task_id).await?;
        Ok(task)
    }
    
    /// Delete a task
    pub async fn delete_task(&self, task_id: i64) -> Result<bool> {
        info!("Deleting task with ID: {}", task_id);
        
        let result = sqlx::query("DELETE FROM tasks WHERE id = ?")
            .bind(task_id)
            .execute(&**self.db_pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to delete task: {}", e)))?;
        
        let deleted = result.rows_affected() > 0;
        if deleted {
            info!("Task {} deleted successfully", task_id);
        } else {
            warn!("Task {} not found for deletion", task_id);
        }
        
        Ok(deleted)
    }
    
    /// Start a task
    pub async fn start_task(&self, task_id: i64) -> Result<Option<Task>> {
        info!("Starting task with ID: {}", task_id);
        
        let result = sqlx::query(
            "UPDATE tasks SET status = 'running', started_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND status = 'pending'"
        )
        .bind(task_id)
        .execute(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to start task: {}", e)))?;
        
        if result.rows_affected() > 0 {
            self.get_task(task_id).await
        } else {
            Ok(None)
        }
    }
    
    /// Stop a task
    pub async fn stop_task(&self, task_id: i64) -> Result<Option<Task>> {
        info!("Stopping task with ID: {}", task_id);
        
        let result = sqlx::query(
            "UPDATE tasks SET status = 'cancelled', completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND status IN ('pending', 'running')"
        )
        .bind(task_id)
        .execute(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to stop task: {}", e)))?;
        
        if result.rows_affected() > 0 {
            self.get_task(task_id).await
        } else {
            Ok(None)
        }
    }
    
    /// Restart a task
    pub async fn restart_task(&self, task_id: i64) -> Result<Option<Task>> {
        info!("Restarting task with ID: {}", task_id);
        
        let result = sqlx::query(
            "UPDATE tasks SET status = 'pending', progress = 0, started_at = NULL, completed_at = NULL, error_message = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(task_id)
        .execute(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to restart task: {}", e)))?;
        
        if result.rows_affected() > 0 {
            self.get_task(task_id).await
        } else {
            Ok(None)
        }
    }
    
    /// Update task progress
    pub async fn update_progress(&self, task_id: i64, progress: i32) -> Result<()> {
        debug!("Updating progress for task {}: {}%", task_id, progress);
        
        sqlx::query("UPDATE tasks SET progress = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
            .bind(progress)
            .bind(task_id)
            .execute(&**self.db_pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to update progress: {}", e)))?;
        
        Ok(())
    }
    
    /// Mark task as completed
    pub async fn complete_task(&self, task_id: i64) -> Result<()> {
        info!("Marking task {} as completed", task_id);
        
        sqlx::query(
            "UPDATE tasks SET status = 'completed', progress = 100, completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(task_id)
        .execute(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to complete task: {}", e)))?;
        
        Ok(())
    }
    
    /// Mark task as failed
    pub async fn fail_task(&self, task_id: i64, error_message: &str) -> Result<()> {
        warn!("Marking task {} as failed: {}", task_id, error_message);
        
        sqlx::query(
            "UPDATE tasks SET status = 'failed', completed_at = CURRENT_TIMESTAMP, error_message = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        )
        .bind(error_message)
        .bind(task_id)
        .execute(&**self.db_pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to fail task: {}", e)))?;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    async fn create_test_service() -> TaskService {
        let temp_file = NamedTempFile::new().unwrap();
        let db_url = format!("sqlite:{}", temp_file.path().display());
        let pool = crate::database::get_database_pool(&db_url).await.unwrap();
        crate::database::initialize_database(&pool).await.unwrap();
        TaskService::new(pool)
    }

    #[tokio::test]
    async fn test_create_and_get_task() {
        let service = create_test_service().await;
        
        let request = CreateTaskRequest {
            name: "Test Task".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "standard".to_string(),
            config: serde_json::json!({}),
        };
        
        let task = service.create_task(&request).await.unwrap();
        assert_eq!(task.name, "Test Task");
        assert_eq!(task.status, "pending");
        
        let retrieved = service.get_task(task.id).await.unwrap().unwrap();
        assert_eq!(retrieved.id, task.id);
        assert_eq!(retrieved.name, task.name);
    }

    #[tokio::test]
    async fn test_task_lifecycle() {
        let service = create_test_service().await;
        
        let request = CreateTaskRequest {
            name: "Lifecycle Test".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "quick".to_string(),
            config: serde_json::json!({}),
        };
        
        let task = service.create_task(&request).await.unwrap();
        
        // Start task
        let started = service.start_task(task.id).await.unwrap().unwrap();
        assert_eq!(started.status, "running");
        
        // Update progress
        service.update_progress(task.id, 50).await.unwrap();
        
        // Complete task
        service.complete_task(task.id).await.unwrap();
        
        let completed = service.get_task(task.id).await.unwrap().unwrap();
        assert_eq!(completed.status, "completed");
        assert_eq!(completed.progress, 100);
    }
}

//! Web API models
//!
//! This module contains data models for HTTP requests and responses.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Standard API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    /// Create a successful response with data
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            timestamp: Utc::now(),
        }
    }

    /// Create a successful response with message
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            timestamp: Utc::now(),
        }
    }
}

/// API error response
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    pub success: bool,
    pub error: ErrorDetails,
    pub timestamp: DateTime<Utc>,
}

/// Error details
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorDetails {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

impl ApiError {
    /// Create a validation error
    pub fn validation_error(message: &str) -> Self {
        Self {
            success: false,
            error: ErrorDetails {
                code: "VALIDATION_ERROR".to_string(),
                message: message.to_string(),
                details: None,
            },
            timestamp: Utc::now(),
        }
    }

    /// Create a not found error
    pub fn not_found(message: &str) -> Self {
        Self {
            success: false,
            error: ErrorDetails {
                code: "NOT_FOUND".to_string(),
                message: message.to_string(),
                details: None,
            },
            timestamp: Utc::now(),
        }
    }

    /// Create an internal server error
    pub fn internal_error(message: &str) -> Self {
        Self {
            success: false,
            error: ErrorDetails {
                code: "INTERNAL_ERROR".to_string(),
                message: message.to_string(),
                details: None,
            },
            timestamp: Utc::now(),
        }
    }

    /// Create a custom error
    pub fn custom(code: String, message: String, details: Option<serde_json::Value>) -> Self {
        Self {
            success: false,
            error: ErrorDetails {
                code,
                message,
                details,
            },
            timestamp: Utc::now(),
        }
    }
}

/// Pagination metadata
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub page: i64,
    pub per_page: i64,
    pub total: i64,
    pub total_pages: i64,
    pub has_next: bool,
    pub has_prev: bool,
}

impl PaginationMeta {
    /// Create pagination metadata
    pub fn new(page: i64, per_page: i64, total: i64) -> Self {
        let total_pages = (total + per_page - 1) / per_page;
        Self {
            page,
            per_page,
            total,
            total_pages,
            has_next: page < total_pages,
            has_prev: page > 1,
        }
    }
}

/// Paginated response
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub success: bool,
    pub data: Vec<T>,
    pub meta: PaginationMeta,
    pub timestamp: DateTime<Utc>,
}

impl<T> PaginatedResponse<T> {
    /// Create a paginated response
    pub fn new(data: Vec<T>, page: i64, per_page: i64, total: i64) -> Self {
        Self {
            success: true,
            data,
            meta: PaginationMeta::new(page, per_page, total),
            timestamp: Utc::now(),
        }
    }
}

/// Task summary for API responses
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskSummaryResponse {
    pub id: i64,
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub status: String,
    pub progress: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub duration: Option<i64>, // Duration in seconds
}

/// Detailed task response
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskDetailResponse {
    pub id: i64,
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub config: serde_json::Value,
    pub status: String,
    pub progress: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub duration: Option<i64>, // Duration in seconds
    pub result_count: i64,
}

/// Scan result response
#[derive(Debug, Serialize, Deserialize)]
pub struct ScanResultResponse {
    pub id: i64,
    pub task_id: i64,
    pub tool: String,
    pub target: String,
    pub result_type: String,
    pub data: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

/// System statistics response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatsResponse {
    pub tasks: TaskStatsResponse,
    pub system: SystemInfoResponse,
    pub database: DatabaseStatsResponse,
}

/// Task statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskStatsResponse {
    pub total: i64,
    pub pending: i64,
    pub running: i64,
    pub completed: i64,
    pub failed: i64,
    pub cancelled: i64,
}

/// System information
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfoResponse {
    pub version: String,
    pub uptime: i64,
    pub memory_usage: Option<i64>,
    pub cpu_usage: Option<f64>,
}

/// Database statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseStatsResponse {
    pub version: String,
    pub size_bytes: i64,
    pub total_results: i64,
}

/// WebSocket message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WebSocketMessage {
    TaskStatusUpdate {
        task_id: i64,
        status: String,
        progress: i32,
        message: Option<String>,
    },
    TaskProgress {
        task_id: i64,
        progress: i32,
        current_target: Option<String>,
        found_ports: Option<Vec<u16>>,
    },
    TaskCompleted {
        task_id: i64,
        duration: i64,
        total_results: i64,
    },
    TaskError {
        task_id: i64,
        error: String,
    },
    SystemNotification {
        level: String, // info, warning, error
        message: String,
    },
}

/// Health check response
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheckResponse {
    pub status: String,
    pub version: String,
    pub database: String,
    pub uptime: i64,
    pub checks: Vec<HealthCheck>,
}

/// Individual health check
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheck {
    pub name: String,
    pub status: String,
    pub message: Option<String>,
    pub duration_ms: Option<i64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_response_creation() {
        let data = "test data";
        let response = ApiResponse::success(data);
        assert!(response.success);
        assert_eq!(response.data, Some("test data"));
        assert!(response.message.is_none());
    }

    #[test]
    fn test_api_error_creation() {
        let error = ApiError::validation_error("Invalid input");
        assert!(!error.success);
        assert_eq!(error.error.code, "VALIDATION_ERROR");
        assert_eq!(error.error.message, "Invalid input");
    }

    #[test]
    fn test_pagination_meta() {
        let meta = PaginationMeta::new(2, 10, 25);
        assert_eq!(meta.page, 2);
        assert_eq!(meta.per_page, 10);
        assert_eq!(meta.total, 25);
        assert_eq!(meta.total_pages, 3);
        assert!(meta.has_next);
        assert!(meta.has_prev);
    }

    #[test]
    fn test_websocket_message_serialization() {
        let message = WebSocketMessage::TaskStatusUpdate {
            task_id: 1,
            status: "running".to_string(),
            progress: 50,
            message: Some("Scanning ports...".to_string()),
        };

        let json = serde_json::to_string(&message).unwrap();
        assert!(json.contains("TaskStatusUpdate"));
        assert!(json.contains("task_id"));

        let deserialized: WebSocketMessage = serde_json::from_str(&json).unwrap();
        match deserialized {
            WebSocketMessage::TaskStatusUpdate { task_id, .. } => {
                assert_eq!(task_id, 1);
            }
            _ => panic!("Wrong message type"),
        }
    }
}

//! Web routes configuration
//!
//! This module defines all HTTP routes for the web API.

use actix_web::{web, HttpResponse, Result};

pub mod config;
pub mod results;
pub mod tasks;
pub mod websocket;

/// Configure all application routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg
        // API v1 routes
        .service(
            web::scope("/api/v1")
                .configure(tasks::configure_routes)
                .configure(results::configure_routes)
                .configure(config::configure_routes),
        )
        // WebSocket routes
        .configure(websocket::configure_routes)
        // Static file serving (for frontend)
        .service(web::scope("/static").route("/{filename:.*}", web::get().to(serve_static_files)))
        // Catch-all route for SPA (Single Page Application)
        .default_service(web::get().to(serve_spa));
}

/// Serve static files (CSS, JS, images, etc.)
async fn serve_static_files(path: web::Path<String>) -> Result<HttpResponse> {
    let filename = path.into_inner();

    // Security check: prevent directory traversal
    if filename.contains("..") || filename.starts_with('/') {
        return Ok(HttpResponse::BadRequest().json(serde_json::json!({
            "error": "Invalid file path"
        })));
    }

    // In a real implementation, you would serve files from a static directory
    // For now, return a placeholder response
    Ok(HttpResponse::NotFound().json(serde_json::json!({
        "error": "Static file serving not implemented yet"
    })))
}

/// Serve the Single Page Application (React frontend)
async fn serve_spa() -> Result<HttpResponse> {
    // In a real implementation, this would serve the React app's index.html
    // For now, return a placeholder HTML page
    let html = r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RustScan Web - Security Scanning Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 600px;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .api-links {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 2rem;
        }
        .api-links h3 {
            margin-top: 0;
        }
        .api-links a {
            color: #fff;
            text-decoration: none;
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            transition: background 0.3s;
        }
        .api-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            background: #4CAF50;
            border-radius: 15px;
            font-size: 0.9rem;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦀 RustScan Web</h1>
        <p>Modern Web Security Scanning Platform</p>
        <p>Backend API is running <span class="status">✓ Online</span></p>
        
        <div class="api-links">
            <h3>API Endpoints</h3>
            <a href="/health">Health Check</a>
            <a href="/version">Version Info</a>
            <a href="/api/v1/tasks">Tasks API</a>
            <a href="/api/v1/results">Results API</a>
            <a href="/api/v1/config">Configuration API</a>
        </div>
        
        <p style="margin-top: 2rem; font-size: 1rem; opacity: 0.7;">
            Frontend React application will be available here once implemented.
        </p>
    </div>
</body>
</html>
    "#;

    Ok(HttpResponse::Ok()
        .content_type("text/html; charset=utf-8")
        .body(html))
}

#[cfg(test)]
mod tests {
    use super::*;
    use actix_web::{test, App};

    #[actix_web::test]
    async fn test_serve_spa() {
        let app = test::init_service(App::new().default_service(web::get().to(serve_spa))).await;

        let req = test::TestRequest::get().uri("/").to_request();
        let resp = test::call_service(&app, req).await;
        assert!(resp.status().is_success());

        let body = test::read_body(resp).await;
        let body_str = std::str::from_utf8(&body).unwrap();
        assert!(body_str.contains("RustScan Web"));
    }

    #[actix_web::test]
    async fn test_static_file_security() {
        let app = test::init_service(
            App::new().route("/static/{filename:.*}", web::get().to(serve_static_files)),
        )
        .await;

        // Test directory traversal protection
        let req = test::TestRequest::get()
            .uri("/static/../etc/passwd")
            .to_request();
        let resp = test::call_service(&app, req).await;
        assert_eq!(resp.status(), 400);

        // Test absolute path protection
        let req = test::TestRequest::get()
            .uri("/static//etc/passwd")
            .to_request();
        let resp = test::call_service(&app, req).await;
        assert_eq!(resp.status(), 400);
    }
}

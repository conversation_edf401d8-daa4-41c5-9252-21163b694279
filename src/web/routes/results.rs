//! Results management routes
//!
//! This module defines HTTP routes for scan results operations.

use actix_web::{web, HttpResponse, Result};
use tracing::{info, debug};

use crate::services::ResultService;
use crate::web::models::{ApiResponse, ApiError};

/// Configure result routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/results")
            .route("", web::get().to(list_all_results))
            .route("/task/{task_id}", web::get().to(get_task_results))
            .route("/task/{task_id}/export", web::get().to(export_task_results))
            .route("/task/{task_id}/stats", web::get().to(get_result_stats))
            .route("/task/{task_id}", web::delete().to(delete_task_results))
    );
}

/// List all results (with pagination)
async fn list_all_results() -> Result<HttpResponse> {
    // Placeholder implementation
    let response = ApiResponse::success(Vec::<serde_json::Value>::new());
    Ok(HttpResponse::Ok().json(response))
}

/// Get results for a specific task
async fn get_task_results(
    result_service: web::Data<ResultService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    debug!("Getting results for task {}", task_id);
    
    match result_service.get_task_results(task_id).await {
        Ok(results) => {
            let response = ApiResponse::success(results);
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            let error = ApiError::internal_error("Failed to get results");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Export task results in various formats
async fn export_task_results(
    result_service: web::Data<ResultService>,
    path: web::Path<i64>,
    query: web::Query<ExportQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let format = query.format.as_deref().unwrap_or("json");
    
    info!("Exporting results for task {} in {} format", task_id, format);
    
    let export_format = match format.parse() {
        Ok(fmt) => fmt,
        Err(_) => {
            let error = ApiError::validation_error("Invalid export format");
            return Ok(HttpResponse::BadRequest().json(error));
        }
    };
    
    match result_service.export_results(task_id, export_format).await {
        Ok(exported_data) => {
            let content_type = match format {
                "json" => "application/json",
                "csv" => "text/csv",
                "xml" => "application/xml",
                _ => "text/plain",
            };
            
            Ok(HttpResponse::Ok()
                .content_type(content_type)
                .body(exported_data))
        }
        Err(e) => {
            let error = ApiError::internal_error("Failed to export results");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Get result statistics for a task
async fn get_result_stats(
    result_service: web::Data<ResultService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    debug!("Getting result stats for task {}", task_id);
    
    match result_service.get_result_stats(task_id).await {
        Ok(stats) => {
            let response = ApiResponse::success(stats);
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            let error = ApiError::internal_error("Failed to get result stats");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Delete results for a task
async fn delete_task_results(
    result_service: web::Data<ResultService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Deleting results for task {}", task_id);
    
    match result_service.delete_task_results(task_id).await {
        Ok(deleted_count) => {
            let response = ApiResponse::success(serde_json::json!({
                "message": format!("Deleted {} results", deleted_count),
                "deleted_count": deleted_count
            }));
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            let error = ApiError::internal_error("Failed to delete results");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Query parameters for export
#[derive(serde::Deserialize)]
struct ExportQuery {
    format: Option<String>,
}

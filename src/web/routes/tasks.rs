//! Task management routes
//!
//! This module defines HTTP routes for task management operations.

use actix_web::{web, HttpResponse, Result};
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

use crate::services::TaskService;
use crate::web::models::{ApiResponse, ApiError};

/// Configure task routes
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/tasks")
            .route("", web::get().to(list_tasks))
            .route("", web::post().to(create_task))
            .route("/{id}", web::get().to(get_task))
            .route("/{id}", web::put().to(update_task))
            .route("/{id}", web::delete().to(delete_task))
            .route("/{id}/start", web::post().to(start_task))
            .route("/{id}/stop", web::post().to(stop_task))
            .route("/{id}/restart", web::post().to(restart_task))
    );
}

/// List all tasks with optional filtering
async fn list_tasks(
    task_service: web::Data<TaskService>,
    query: web::Query<TaskListQuery>,
) -> Result<HttpResponse> {
    info!("Listing tasks with filters: {:?}", query);
    
    match task_service.list_tasks(&query).await {
        Ok(tasks) => {
            let response = ApiResponse::success(tasks);
            Ok(HttpResponse::Ok().json(response))
        }
        Err(e) => {
            warn!("Failed to list tasks: {}", e);
            let error = ApiError::internal_error("Failed to list tasks");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Create a new scanning task
async fn create_task(
    task_service: web::Data<TaskService>,
    request: web::Json<CreateTaskRequest>,
) -> Result<HttpResponse> {
    info!("Creating new task: {}", request.name);
    
    // Validate request
    if let Err(validation_error) = request.validate() {
        let error = ApiError::validation_error(&validation_error);
        return Ok(HttpResponse::BadRequest().json(error));
    }
    
    match task_service.create_task(&request).await {
        Ok(task) => {
            info!("Task created successfully with ID: {}", task.id);
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Created().json(response))
        }
        Err(e) => {
            warn!("Failed to create task: {}", e);
            let error = ApiError::internal_error("Failed to create task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Get a specific task by ID
async fn get_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Getting task with ID: {}", task_id);
    
    match task_service.get_task(task_id).await {
        Ok(Some(task)) => {
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to get task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to get task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Update a task
async fn update_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
    request: web::Json<UpdateTaskRequest>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Updating task with ID: {}", task_id);
    
    match task_service.update_task(task_id, &request).await {
        Ok(Some(task)) => {
            info!("Task {} updated successfully", task_id);
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to update task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to update task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Delete a task
async fn delete_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Deleting task with ID: {}", task_id);
    
    match task_service.delete_task(task_id).await {
        Ok(true) => {
            info!("Task {} deleted successfully", task_id);
            let response = ApiResponse::success(serde_json::json!({
                "message": "Task deleted successfully"
            }));
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(false) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to delete task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to delete task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Start a task
async fn start_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Starting task with ID: {}", task_id);
    
    match task_service.start_task(task_id).await {
        Ok(Some(task)) => {
            info!("Task {} started successfully", task_id);
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to start task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to start task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Stop a task
async fn stop_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Stopping task with ID: {}", task_id);
    
    match task_service.stop_task(task_id).await {
        Ok(Some(task)) => {
            info!("Task {} stopped successfully", task_id);
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to stop task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to stop task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Restart a task
async fn restart_task(
    task_service: web::Data<TaskService>,
    path: web::Path<i64>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    info!("Restarting task with ID: {}", task_id);
    
    match task_service.restart_task(task_id).await {
        Ok(Some(task)) => {
            info!("Task {} restarted successfully", task_id);
            let response = ApiResponse::success(task);
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            let error = ApiError::not_found("Task not found");
            Ok(HttpResponse::NotFound().json(error))
        }
        Err(e) => {
            warn!("Failed to restart task {}: {}", task_id, e);
            let error = ApiError::internal_error("Failed to restart task");
            Ok(HttpResponse::InternalServerError().json(error))
        }
    }
}

/// Query parameters for listing tasks
#[derive(Debug, Deserialize)]
pub struct TaskListQuery {
    pub status: Option<String>,
    pub scan_type: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

/// Request body for creating a task
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub config: serde_json::Value,
}

impl CreateTaskRequest {
    /// Validate the create task request
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("Task name cannot be empty".to_string());
        }
        
        if self.target.trim().is_empty() {
            return Err("Target cannot be empty".to_string());
        }
        
        if self.scan_type.trim().is_empty() {
            return Err("Scan type cannot be empty".to_string());
        }
        
        // Validate scan type
        match self.scan_type.as_str() {
            "quick" | "standard" | "deep" | "web" | "custom" => {}
            _ => return Err("Invalid scan type".to_string()),
        }
        
        Ok(())
    }
}

/// Request body for updating a task
#[derive(Debug, Deserialize)]
pub struct UpdateTaskRequest {
    pub name: Option<String>,
    pub config: Option<serde_json::Value>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_task_validation() {
        let valid_request = CreateTaskRequest {
            name: "Test Task".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "standard".to_string(),
            config: serde_json::json!({}),
        };
        assert!(valid_request.validate().is_ok());
        
        let invalid_request = CreateTaskRequest {
            name: "".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "standard".to_string(),
            config: serde_json::json!({}),
        };
        assert!(invalid_request.validate().is_err());
        
        let invalid_scan_type = CreateTaskRequest {
            name: "Test".to_string(),
            target: "127.0.0.1".to_string(),
            scan_type: "invalid".to_string(),
            config: serde_json::json!({}),
        };
        assert!(invalid_scan_type.validate().is_err());
    }
}

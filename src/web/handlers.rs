//! HTTP request handlers
//!
//! This module contains common HTTP request handlers and utilities.

use actix_web::{web, HttpResponse, Result};
use tracing::{debug, info};

use crate::web::models::{ApiError, ApiResponse};

/// Health check handler
pub async fn health_check() -> Result<HttpResponse> {
    debug!("Health check requested");

    let response = ApiResponse::success(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION")
    }));

    Ok(HttpResponse::Ok().json(response))
}

/// System status handler
pub async fn system_status() -> Result<HttpResponse> {
    debug!("System status requested");

    let response = ApiResponse::success(serde_json::json!({
        "status": "operational",
        "uptime": get_uptime_seconds(),
        "memory_usage": get_memory_usage(),
        "active_tasks": 0, // Would be populated from task service
        "timestamp": chrono::Utc::now()
    }));

    Ok(HttpResponse::Ok().json(response))
}

/// Not found handler
pub async fn not_found() -> Result<HttpResponse> {
    let error = ApiError::not_found("Endpoint not found");
    Ok(HttpResponse::NotFound().json(error))
}

/// Method not allowed handler
pub async fn method_not_allowed() -> Result<HttpResponse> {
    let error = ApiError::custom(
        "METHOD_NOT_ALLOWED".to_string(),
        "HTTP method not allowed for this endpoint".to_string(),
        None,
    );
    Ok(HttpResponse::MethodNotAllowed().json(error))
}

/// Internal server error handler
pub async fn internal_server_error() -> Result<HttpResponse> {
    let error = ApiError::internal_error("Internal server error occurred");
    Ok(HttpResponse::InternalServerError().json(error))
}

/// Get system uptime in seconds
fn get_uptime_seconds() -> u64 {
    use std::time::{SystemTime, UNIX_EPOCH};
    static START_TIME: std::sync::OnceLock<u64> = std::sync::OnceLock::new();

    let start = START_TIME.get_or_init(|| {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    });

    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
        .saturating_sub(*start)
}

/// Get memory usage information (placeholder)
fn get_memory_usage() -> serde_json::Value {
    // In a real implementation, this would use system APIs to get actual memory usage
    serde_json::json!({
        "used_mb": 0,
        "total_mb": 0,
        "percentage": 0.0
    })
}

/// CORS preflight handler
pub async fn cors_preflight() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok()
        .insert_header(("Access-Control-Allow-Origin", "*"))
        .insert_header((
            "Access-Control-Allow-Methods",
            "GET, POST, PUT, DELETE, OPTIONS",
        ))
        .insert_header((
            "Access-Control-Allow-Headers",
            "Content-Type, Authorization",
        ))
        .insert_header(("Access-Control-Max-Age", "3600"))
        .finish())
}

/// Generic error response helper
pub fn error_response(status_code: u16, message: &str) -> HttpResponse {
    let error = match status_code {
        400 => ApiError::validation_error(message),
        404 => ApiError::not_found(message),
        500 => ApiError::internal_error(message),
        _ => ApiError::custom("UNKNOWN_ERROR".to_string(), message.to_string(), None),
    };

    HttpResponse::build(
        actix_web::http::StatusCode::from_u16(status_code)
            .unwrap_or(actix_web::http::StatusCode::INTERNAL_SERVER_ERROR),
    )
    .json(error)
}

/// Success response helper
pub fn success_response<T: serde::Serialize>(data: T) -> HttpResponse {
    let response = ApiResponse::success(data);
    HttpResponse::Ok().json(response)
}

/// Success response with message helper
pub fn success_response_with_message<T: serde::Serialize>(
    data: T,
    message: String,
) -> HttpResponse {
    let response = ApiResponse::success_with_message(data, message);
    HttpResponse::Ok().json(response)
}

#[cfg(test)]
mod tests {
    use super::*;
    use actix_web::{test, App};

    #[actix_web::test]
    async fn test_health_check() {
        let app =
            test::init_service(App::new().route("/health", web::get().to(health_check))).await;

        let req = test::TestRequest::get().uri("/health").to_request();
        let resp = test::call_service(&app, req).await;
        assert!(resp.status().is_success());
    }

    #[actix_web::test]
    async fn test_system_status() {
        let app =
            test::init_service(App::new().route("/status", web::get().to(system_status))).await;

        let req = test::TestRequest::get().uri("/status").to_request();
        let resp = test::call_service(&app, req).await;
        assert!(resp.status().is_success());
    }

    #[test]
    fn test_uptime() {
        let uptime1 = get_uptime_seconds();
        std::thread::sleep(std::time::Duration::from_millis(10));
        let uptime2 = get_uptime_seconds();
        assert!(uptime2 >= uptime1);
    }

    #[test]
    fn test_error_response() {
        let response = error_response(404, "Not found");
        assert_eq!(response.status(), 404);
    }

    #[test]
    fn test_success_response() {
        let data = serde_json::json!({"test": "data"});
        let response = success_response(data);
        assert_eq!(response.status(), 200);
    }
}

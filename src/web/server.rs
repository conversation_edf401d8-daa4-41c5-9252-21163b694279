//! Web server configuration and setup
//!
//! This module provides the main web server setup using Actix-web.

use actix_web::{web, App, HttpServer, middleware, Result};
use actix_cors::Cors;
use tracing::{info, warn};

use crate::common::{config::AppConfig, error::AppError};
use crate::database::DatabasePool;
use crate::services::{TaskService, ScanService, ResultService, NotificationService};

/// Create and configure the Actix-web application
pub fn create_app(
    db_pool: DatabasePool,
    task_service: TaskService,
    scan_service: ScanService,
    result_service: ResultService,
    notification_service: NotificationService,
) -> App<
    impl actix_web::dev::ServiceFactory<
        actix_web::dev::ServiceRequest,
        Config = (),
        Response = actix_web::dev::ServiceResponse,
        Error = actix_web::Error,
        InitError = (),
    >,
> {
    App::new()
        // Add application data
        .app_data(web::Data::new(db_pool))
        .app_data(web::Data::new(task_service))
        .app_data(web::Data::new(scan_service))
        .app_data(web::Data::new(result_service))
        .app_data(web::Data::new(notification_service))
        
        // Add middleware
        .wrap(middleware::Logger::default())
        .wrap(middleware::NormalizePath::trim())
        .wrap(middleware::DefaultHeaders::new()
            .add(("X-Version", env!("CARGO_PKG_VERSION")))
            .add(("X-Powered-By", "RustScan-Web"))
        )
        .wrap(
            Cors::default()
                .allow_any_origin()
                .allow_any_method()
                .allow_any_header()
                .max_age(3600)
        )
        
        // Configure routes
        .configure(super::routes::configure_routes)
        
        // Health check endpoint
        .route("/health", web::get().to(health_check))
        .route("/version", web::get().to(version_info))
}

/// Start the HTTP server
pub async fn start_server(config: AppConfig) -> Result<(), AppError> {
    let bind_address = format!("{}:{}", config.host, config.port);
    info!("Starting HTTP server on {}", bind_address);
    
    // Initialize database
    let db_pool = crate::database::get_database_pool(&config.database_url).await?;
    crate::database::initialize_database(&db_pool).await?;
    
    // Initialize services
    let task_service = TaskService::new(db_pool.clone());
    let scan_service = ScanService::new();
    let result_service = ResultService::new(db_pool.clone());
    let notification_service = NotificationService::new();
    
    // Create and start server
    HttpServer::new(move || {
        create_app(
            db_pool.clone(),
            task_service.clone(),
            scan_service.clone(),
            result_service.clone(),
            notification_service.clone(),
        )
    })
    .bind(&bind_address)
    .map_err(|e| AppError::ServerError(format!("Failed to bind to {}: {}", bind_address, e)))?
    .run()
    .await
    .map_err(|e| AppError::ServerError(format!("Server error: {}", e)))?;
    
    Ok(())
}

/// Health check endpoint
async fn health_check(
    db_pool: web::Data<DatabasePool>,
) -> Result<web::Json<HealthResponse>, actix_web::Error> {
    let mut response = HealthResponse {
        status: "ok".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        database: "ok".to_string(),
        uptime: get_uptime(),
    };
    
    // Check database connection
    match crate::database::test_connection(&db_pool).await {
        Ok(_) => {
            info!("Health check: all systems operational");
        }
        Err(e) => {
            warn!("Health check: database connection failed: {}", e);
            response.status = "degraded".to_string();
            response.database = "error".to_string();
        }
    }
    
    Ok(web::Json(response))
}

/// Version information endpoint
async fn version_info() -> Result<web::Json<VersionResponse>, actix_web::Error> {
    Ok(web::Json(VersionResponse {
        name: env!("CARGO_PKG_NAME").to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        description: env!("CARGO_PKG_DESCRIPTION").to_string(),
        authors: env!("CARGO_PKG_AUTHORS").split(':').map(|s| s.to_string()).collect(),
        repository: env!("CARGO_PKG_REPOSITORY").to_string(),
        license: env!("CARGO_PKG_LICENSE").to_string(),
        rust_version: option_env!("CARGO_PKG_RUST_VERSION").unwrap_or("unknown").to_string(),
        build_time: option_env!("VERGEN_BUILD_TIMESTAMP").unwrap_or("unknown").to_string(),
        git_hash: option_env!("VERGEN_GIT_SHA").unwrap_or("unknown").to_string(),
    }))
}

/// Get application uptime
fn get_uptime() -> u64 {
    use std::time::{SystemTime, UNIX_EPOCH};
    static START_TIME: std::sync::OnceLock<u64> = std::sync::OnceLock::new();
    
    let start = START_TIME.get_or_init(|| {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    });
    
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
        .saturating_sub(*start)
}

/// Health check response
#[derive(serde::Serialize)]
struct HealthResponse {
    status: String,
    version: String,
    database: String,
    uptime: u64,
}

/// Version information response
#[derive(serde::Serialize)]
struct VersionResponse {
    name: String,
    version: String,
    description: String,
    authors: Vec<String>,
    repository: String,
    license: String,
    rust_version: String,
    build_time: String,
    git_hash: String,
}

/// Configure server with custom settings
pub struct ServerConfig {
    pub workers: Option<usize>,
    pub max_connections: Option<usize>,
    pub keep_alive: Option<std::time::Duration>,
    pub client_timeout: Option<std::time::Duration>,
    pub shutdown_timeout: Option<std::time::Duration>,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            workers: None, // Use default (number of CPU cores)
            max_connections: Some(25000),
            keep_alive: Some(std::time::Duration::from_secs(75)),
            client_timeout: Some(std::time::Duration::from_secs(5)),
            shutdown_timeout: Some(std::time::Duration::from_secs(30)),
        }
    }
}

/// Start server with custom configuration
pub async fn start_server_with_config(
    app_config: AppConfig,
    server_config: ServerConfig,
) -> Result<(), AppError> {
    let bind_address = format!("{}:{}", app_config.host, app_config.port);
    info!("Starting HTTP server on {} with custom config", bind_address);
    
    // Initialize database
    let db_pool = crate::database::get_database_pool(&app_config.database_url).await?;
    crate::database::initialize_database(&db_pool).await?;
    
    // Initialize services
    let task_service = TaskService::new(db_pool.clone());
    let scan_service = ScanService::new();
    let result_service = ResultService::new(db_pool.clone());
    let notification_service = NotificationService::new();
    
    // Create server with custom configuration
    let mut server = HttpServer::new(move || {
        create_app(
            db_pool.clone(),
            task_service.clone(),
            scan_service.clone(),
            result_service.clone(),
            notification_service.clone(),
        )
    });
    
    // Apply custom configuration
    if let Some(workers) = server_config.workers {
        server = server.workers(workers);
    }
    
    if let Some(max_conn) = server_config.max_connections {
        server = server.max_connections(max_conn);
    }
    
    if let Some(keep_alive) = server_config.keep_alive {
        server = server.keep_alive(keep_alive);
    }
    
    if let Some(timeout) = server_config.client_timeout {
        server = server.client_request_timeout(timeout);
    }
    
    if let Some(shutdown) = server_config.shutdown_timeout {
        server = server.shutdown_timeout(shutdown.as_secs());
    }
    
    server
        .bind(&bind_address)
        .map_err(|e| AppError::ServerError(format!("Failed to bind to {}: {}", bind_address, e)))?
        .run()
        .await
        .map_err(|e| AppError::ServerError(format!("Server error: {}", e)))?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use actix_web::{test, App};

    #[actix_web::test]
    async fn test_health_check() {
        // This would require setting up a test database
        // For now, just test that the endpoint exists
        assert!(true);
    }

    #[actix_web::test]
    async fn test_version_info() {
        let app = test::init_service(
            App::new().route("/version", web::get().to(version_info))
        ).await;
        
        let req = test::TestRequest::get().uri("/version").to_request();
        let resp = test::call_service(&app, req).await;
        assert!(resp.status().is_success());
    }

    #[test]
    fn test_uptime() {
        let uptime1 = get_uptime();
        std::thread::sleep(std::time::Duration::from_millis(10));
        let uptime2 = get_uptime();
        assert!(uptime2 >= uptime1);
    }
}

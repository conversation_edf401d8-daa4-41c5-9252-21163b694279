//! Application configuration
//!
//! This module handles loading and managing application configuration.

use super::error::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::env;

/// Main application configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Server host address
    pub host: String,

    /// Server port
    pub port: u16,

    /// Database URL
    pub database_url: String,

    /// Maximum number of concurrent tasks
    pub max_concurrent_tasks: usize,

    /// Default scan timeout in milliseconds
    pub default_timeout: u64,

    /// Enable WebSocket notifications
    pub enable_notifications: bool,

    /// Log level
    pub log_level: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        AppConfig {
            host: "127.0.0.1".to_string(),
            port: 8080,
            database_url: "sqlite:./rustscan.db".to_string(),
            max_concurrent_tasks: 5,
            default_timeout: 5000,
            enable_notifications: true,
            log_level: "info".to_string(),
        }
    }
}

impl AppConfig {
    /// Load configuration from environment variables and defaults
    pub async fn load() -> Result<Self> {
        let mut config = AppConfig::default();

        // Override with environment variables if present
        if let Ok(host) = env::var("RUSTSCAN_HOST") {
            config.host = host;
        }

        if let Ok(port) = env::var("RUSTSCAN_PORT") {
            config.port = port
                .parse()
                .map_err(|e| AppError::config(format!("Invalid port: {}", e)))?;
        }

        if let Ok(db_url) = env::var("DATABASE_URL") {
            config.database_url = db_url;
        }

        if let Ok(max_tasks) = env::var("MAX_CONCURRENT_TASKS") {
            config.max_concurrent_tasks = max_tasks
                .parse()
                .map_err(|e| AppError::config(format!("Invalid max_concurrent_tasks: {}", e)))?;
        }

        if let Ok(timeout) = env::var("DEFAULT_TIMEOUT") {
            config.default_timeout = timeout
                .parse()
                .map_err(|e| AppError::config(format!("Invalid default_timeout: {}", e)))?;
        }

        if let Ok(notifications) = env::var("ENABLE_NOTIFICATIONS") {
            config.enable_notifications = notifications
                .parse()
                .map_err(|e| AppError::config(format!("Invalid enable_notifications: {}", e)))?;
        }

        if let Ok(log_level) = env::var("LOG_LEVEL") {
            config.log_level = log_level;
        }

        Ok(config)
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<()> {
        if self.port == 0 {
            return Err(AppError::config("Port cannot be 0"));
        }

        if self.max_concurrent_tasks == 0 {
            return Err(AppError::config("max_concurrent_tasks cannot be 0"));
        }

        if self.default_timeout == 0 {
            return Err(AppError::config("default_timeout cannot be 0"));
        }

        Ok(())
    }
}

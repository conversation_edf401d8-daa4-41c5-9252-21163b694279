//! Common error types and handling
//!
//! This module provides unified error handling across the application.

use std::fmt;

/// Main application error type
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Database error: {0}")]
    DatabaseError(String),

    #[error("Server error: {0}")]
    ServerError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("SQL error: {0}")]
    SqlError(#[from] sqlx::Error),

    #[error("Internal error: {0}")]
    InternalError(String),

    #[error("Not found: {0}")]
    NotFound(String),
}

/// Result type alias for convenience
pub type Result<T> = std::result::Result<T, AppError>;

impl AppError {
    /// Create a new configuration error
    pub fn config<S: Into<String>>(msg: S) -> Self {
        AppError::ConfigError(msg.into())
    }

    /// Create a new network error
    pub fn network<S: Into<String>>(msg: S) -> Self {
        AppError::NetworkError(msg.into())
    }

    /// Create a new database error
    pub fn database<S: Into<String>>(msg: S) -> Self {
        AppError::DatabaseError(msg.into())
    }

    /// Create a new validation error
    pub fn validation<S: Into<String>>(msg: S) -> Self {
        AppError::ValidationError(msg.into())
    }

    /// Create a new internal error
    pub fn internal<S: Into<String>>(msg: S) -> Self {
        AppError::InternalError(msg.into())
    }

    /// Create a new internal error (alternative name)
    pub fn internal_error<S: Into<String>>(msg: S) -> Self {
        AppError::InternalError(msg.into())
    }

    /// Create a new not found error
    pub fn not_found<S: Into<String>>(msg: S) -> Self {
        AppError::NotFound(msg.into())
    }
}

/// Convert AppError to HTTP status code for web responses
impl AppError {
    pub fn status_code(&self) -> u16 {
        match self {
            AppError::ConfigError(_) => 500,
            AppError::NetworkError(_) => 502,
            AppError::DatabaseError(_) => 500,
            AppError::ServerError(_) => 500,
            AppError::ValidationError(_) => 400,
            AppError::IoError(_) => 500,
            AppError::JsonError(_) => 400,
            AppError::SqlError(_) => 500,
            AppError::InternalError(_) => 500,
            AppError::NotFound(_) => 404,
        }
    }
}

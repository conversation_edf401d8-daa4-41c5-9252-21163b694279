//! Common utility functions
//!
//! This module provides various utility functions used across the application.

use std::time::{Duration, SystemTime, UNIX_EPOCH};
use uuid::Uuid;

/// Generate a new UUID v4
pub fn generate_uuid() -> String {
    Uuid::new_v4().to_string()
}

/// Get current timestamp in milliseconds
pub fn current_timestamp_ms() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_millis() as u64
}

/// Format duration as human readable string
pub fn format_duration(duration: Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    let millis = duration.subsec_millis();

    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else if seconds > 0 {
        format!("{}.{}s", seconds, millis / 100)
    } else {
        format!("{}ms", millis)
    }
}

/// Validate IP address string
pub fn is_valid_ip(ip: &str) -> bool {
    ip.parse::<std::net::IpAddr>().is_ok()
}

/// Validate port number
pub fn is_valid_port(port: u16) -> bool {
    port > 0
}

/// Validate port range
pub fn is_valid_port_range(start: u16, end: u16) -> bool {
    start > 0 && end > 0 && start <= end
}

/// Parse comma-separated list of ports
pub fn parse_ports(ports_str: &str) -> Result<Vec<u16>, String> {
    let mut ports = Vec::new();

    for part in ports_str.split(',') {
        let part = part.trim();
        if part.is_empty() {
            continue;
        }

        if part.contains('-') {
            // Handle port range like "80-90"
            let range_parts: Vec<&str> = part.split('-').collect();
            if range_parts.len() != 2 {
                return Err(format!("Invalid port range: {}", part));
            }

            let start: u16 = range_parts[0]
                .trim()
                .parse()
                .map_err(|_| format!("Invalid port number: {}", range_parts[0]))?;
            let end: u16 = range_parts[1]
                .trim()
                .parse()
                .map_err(|_| format!("Invalid port number: {}", range_parts[1]))?;

            if !is_valid_port_range(start, end) {
                return Err(format!("Invalid port range: {}-{}", start, end));
            }

            for port in start..=end {
                ports.push(port);
            }
        } else {
            // Handle single port
            let port: u16 = part
                .parse()
                .map_err(|_| format!("Invalid port number: {}", part))?;

            if !is_valid_port(port) {
                return Err(format!("Invalid port number: {}", port));
            }

            ports.push(port);
        }
    }

    Ok(ports)
}

/// Sanitize string for safe usage in commands
pub fn sanitize_string(input: &str) -> String {
    input
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '.' || *c == '-' || *c == '_')
        .collect()
}

/// Calculate percentage
pub fn calculate_percentage(current: usize, total: usize) -> f64 {
    if total == 0 {
        0.0
    } else {
        (current as f64 / total as f64) * 100.0
    }
}

/// Truncate string to specified length
pub fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}

/// Convert bytes to human readable format
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_uuid() {
        let uuid1 = generate_uuid();
        let uuid2 = generate_uuid();
        assert_ne!(uuid1, uuid2);
        assert_eq!(uuid1.len(), 36); // Standard UUID length
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(Duration::from_millis(500)), "500ms");
        assert_eq!(format_duration(Duration::from_secs(5)), "5.0s");
        assert_eq!(format_duration(Duration::from_secs(65)), "1m 5s");
        assert_eq!(format_duration(Duration::from_secs(3665)), "1h 1m 5s");
    }

    #[test]
    fn test_is_valid_ip() {
        assert!(is_valid_ip("127.0.0.1"));
        assert!(is_valid_ip("::1"));
        assert!(!is_valid_ip("invalid"));
        assert!(!is_valid_ip("256.1.1.1"));
    }

    #[test]
    fn test_parse_ports() {
        assert_eq!(parse_ports("80,443").unwrap(), vec![80, 443]);
        assert_eq!(parse_ports("80-82").unwrap(), vec![80, 81, 82]);
        assert_eq!(
            parse_ports("80,443,8080-8082").unwrap(),
            vec![80, 443, 8080, 8081, 8082]
        );
        assert!(parse_ports("invalid").is_err());
        assert!(parse_ports("80-").is_err());
    }

    #[test]
    fn test_sanitize_string() {
        assert_eq!(sanitize_string("test.example.com"), "test.example.com");
        assert_eq!(sanitize_string("test@#$%"), "test");
        assert_eq!(sanitize_string("***********"), "***********");
    }

    #[test]
    fn test_calculate_percentage() {
        assert_eq!(calculate_percentage(50, 100), 50.0);
        assert_eq!(calculate_percentage(0, 100), 0.0);
        assert_eq!(calculate_percentage(100, 100), 100.0);
        assert_eq!(calculate_percentage(1, 0), 0.0);
    }

    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(512), "512 B");
        assert_eq!(format_bytes(1024), "1.00 KB");
        assert_eq!(format_bytes(1536), "1.50 KB");
        assert_eq!(format_bytes(1048576), "1.00 MB");
    }
}

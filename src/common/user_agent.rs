//! User-Agent management for HTTP requests
//!
//! This module provides user-agent strings for various scanning tools.


use std::sync::OnceLock;

/// Common user-agent strings for web scanning
static USER_AGENTS: &[&str] = &[
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
];

/// RustScan specific user-agent
static RUSTSCAN_USER_AGENT: OnceLock<String> = OnceLock::new();

/// User-Agent manager for HTTP requests
pub struct UserAgentManager;

impl UserAgentManager {
    /// Get a random user-agent string
    pub fn random() -> &'static str {
        let mut rng = rand::thread_rng();
        use rand::seq::SliceRandom;
        USER_AGENTS.choose(&mut rng).unwrap_or(&USER_AGENTS[0])
    }

    /// Get the RustScan specific user-agent
    pub fn rustscan() -> &'static str {
        RUSTSCAN_USER_AGENT.get_or_init(|| {
            format!(
                "RustScan/{} (https://github.com/RustScan/RustScan)",
                env!("CARGO_PKG_VERSION")
            )
        })
    }

    /// Get a Chrome user-agent
    pub fn chrome() -> &'static str {
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    /// Get a Firefox user-agent
    pub fn firefox() -> &'static str {
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    }

    /// Get a Safari user-agent
    pub fn safari() -> &'static str {
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
    }

    /// Get a mobile user-agent
    pub fn mobile() -> &'static str {
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_user_agent() {
        let ua = UserAgentManager::random();
        assert!(!ua.is_empty());
        assert!(USER_AGENTS.contains(&ua));
    }

    #[test]
    fn test_rustscan_user_agent() {
        let ua = UserAgentManager::rustscan();
        assert!(ua.starts_with("RustScan/"));
        assert!(ua.contains("github.com"));
    }

    #[test]
    fn test_specific_user_agents() {
        assert!(UserAgentManager::chrome().contains("Chrome"));
        assert!(UserAgentManager::firefox().contains("Firefox"));
        assert!(UserAgentManager::safari().contains("Safari"));
        assert!(UserAgentManager::mobile().contains("iPhone"));
    }
}

//! HTTPx tool integration

use super::SecurityTool;
use crate::common::error::AppError;

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct HttpxConfig {
    pub urls: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct HttpxTool;

impl SecurityTool for HttpxTool {
    type Config = HttpxConfig;
    type Result = serde_json::Value;
    type Error = AppError;

    async fn execute(
        &self,
        _config: Self::Config,
    ) -> std::result::Result<Self::Result, Self::Error> {
        Err(AppError::internal_error(
            "HTTPx integration not yet implemented",
        ))
    }

    fn name(&self) -> &'static str {
        "httpx"
    }

    fn version(&self) -> Option<String> {
        None
    }
}

impl Default for HttpxTool {
    fn default() -> Self {
        Self
    }
}

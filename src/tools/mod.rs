//! External security tool integrations
//!
//! This module provides integrations with various security scanning tools
//! including Nmap, DNSx, Subfinder, HTTPx, and Nuclei.

pub mod rustscan;
pub mod nmap;
pub mod dnsx;
pub mod subfinder;
pub mod httpx;
pub mod nuclei;
pub mod workflow;
pub mod results;

// Common tool interface
pub trait SecurityTool {
    type Config;
    type Result;
    type Error;

    async fn execute(&self, config: Self::Config) -> Result<Self::Result, Self::Error>;
    fn name(&self) -> &'static str;
    fn version(&self) -> Option<String>;
}

// Tool execution result
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ToolResult {
    pub tool_name: String,
    pub target: String,
    pub data: serde_json::Value,
    pub execution_time: std::time::Duration,
    pub success: bool,
    pub error_message: Option<String>,
}

// Workflow coordinator
pub use workflow::WorkflowCoordinator;
pub use results::ResultAnalyzer;

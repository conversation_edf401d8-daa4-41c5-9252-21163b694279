//! RustScan tool integration
//!
//! This module provides integration with the core RustScan functionality.

use std::time::Duration;
use tracing::{debug, info};

use super::{SecurityTool, ToolResult};
use crate::common::error::{AppError, Result};
use crate::core::{ScanConfig, Scanner};

/// RustScan tool wrapper
#[derive(Debug, Clone)]
pub struct RustScanTool;

impl SecurityTool for RustScanTool {
    type Config = ScanConfig;
    type Result = Vec<std::net::SocketAddr>;
    type Error = AppError;

    async fn execute(
        &self,
        config: Self::Config,
    ) -> std::result::Result<Self::Result, Self::Error> {
        info!("Executing RustScan with config: {:?}", config);

        let scanner = Scanner::from_config(&config).await?;
        let results = scanner.run().await;

        debug!("RustScan found {} open ports", results.len());
        Ok(results)
    }

    fn name(&self) -> &'static str {
        "rustscan"
    }

    fn version(&self) -> Option<String> {
        Some(env!("CARGO_PKG_VERSION").to_string())
    }
}

impl RustScanTool {
    /// Create a new RustScan tool instance
    pub fn new() -> Self {
        Self
    }

    /// Execute scan and return formatted results
    pub async fn scan_and_format(&self, config: ScanConfig) -> Result<ToolResult> {
        let start_time = std::time::Instant::now();

        match self.execute(config.clone()).await {
            Ok(sockets) => {
                let execution_time = start_time.elapsed();
                let data = serde_json::json!({
                    "open_ports": sockets.iter().map(|s| {
                        serde_json::json!({
                            "ip": s.ip().to_string(),
                            "port": s.port(),
                            "protocol": "tcp"
                        })
                    }).collect::<Vec<_>>(),
                    "total_found": sockets.len()
                });

                Ok(ToolResult {
                    tool_name: self.name().to_string(),
                    target: config.targets.join(","),
                    data,
                    execution_time,
                    success: true,
                    error_message: None,
                })
            }
            Err(e) => {
                let execution_time = start_time.elapsed();
                Ok(ToolResult {
                    tool_name: self.name().to_string(),
                    target: config.targets.join(","),
                    data: serde_json::json!({}),
                    execution_time,
                    success: false,
                    error_message: Some(e.to_string()),
                })
            }
        }
    }
}

impl Default for RustScanTool {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::config::PortRange;

    #[tokio::test]
    async fn test_rustscan_tool() {
        let tool = RustScanTool::new();
        assert_eq!(tool.name(), "rustscan");
        assert!(tool.version().is_some());
    }

    #[tokio::test]
    async fn test_scan_and_format() {
        let tool = RustScanTool::new();
        let mut config = ScanConfig::new(vec!["127.0.0.1".to_string()]);
        config.range = Some(PortRange { start: 80, end: 80 });

        let result = tool.scan_and_format(config).await.unwrap();
        assert_eq!(result.tool_name, "rustscan");
        assert!(!result.target.is_empty());
    }
}

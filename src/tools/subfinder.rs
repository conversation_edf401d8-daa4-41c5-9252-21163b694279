//! Subfinder tool integration

use crate::common::error::AppError;
use super::SecurityTool;

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SubfinderConfig {
    pub domain: String,
}

#[derive(Debug, Clone)]
pub struct SubfinderTool;

impl SecurityTool for SubfinderTool {
    type Config = SubfinderConfig;
    type Result = serde_json::Value;
    type Error = AppError;

    async fn execute(&self, _config: Self::Config) -> std::result::Result<Self::Result, Self::Error> {
        Err(AppError::internal_error("Subfinder integration not yet implemented"))
    }

    fn name(&self) -> &'static str {
        "subfinder"
    }

    fn version(&self) -> Option<String> {
        None
    }
}

impl Default for SubfinderTool {
    fn default() -> Self {
        Self
    }
}

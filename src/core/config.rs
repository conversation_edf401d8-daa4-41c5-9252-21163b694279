//! Core scanning configuration
//!
//! This module provides configuration types and utilities for scanning operations,
//! adapted from the original RustScan input module.

use serde::{Deserialize, Serialize};
use std::time::Duration;

/// Represents the strategy in which the port scanning will run.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum ScanOrder {
    Serial,
    Random,
}

impl Default for ScanOrder {
    fn default() -> Self {
        ScanOrder::Serial
    }
}

/// Represents the range of ports to be scanned.
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub struct PortRange {
    pub start: u16,
    pub end: u16,
}

impl Default for PortRange {
    fn default() -> Self {
        PortRange {
            start: 1,
            end: 65535,
        }
    }
}

/// Core scanning configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ScanConfig {
    /// Target addresses to scan
    pub targets: Vec<String>,

    /// Specific ports to scan (overrides range)
    pub ports: Option<Vec<u16>>,

    /// Port range to scan
    pub range: Option<PortRange>,

    /// Batch size for concurrent scanning
    pub batch_size: u16,

    /// Timeout for each port scan
    pub timeout: Duration,

    /// Number of retry attempts
    pub tries: u8,

    /// Scan order strategy
    pub scan_order: ScanOrder,

    /// Ports to exclude from scanning
    pub exclude_ports: Vec<u16>,

    /// Whether to perform UDP scanning
    pub udp: bool,

    /// DNS resolver addresses
    pub resolvers: Option<Vec<String>>,

    /// Addresses to exclude from scanning
    pub exclude_addresses: Option<Vec<String>>,
}

impl Default for ScanConfig {
    fn default() -> Self {
        ScanConfig {
            targets: Vec::new(),
            ports: None,
            range: Some(PortRange::default()),
            batch_size: 4500,
            timeout: Duration::from_millis(1500),
            tries: 1,
            scan_order: ScanOrder::Serial,
            exclude_ports: Vec::new(),
            udp: false,
            resolvers: None,
            exclude_addresses: None,
        }
    }
}

impl ScanConfig {
    /// Create a new scan configuration with targets
    pub fn new(targets: Vec<String>) -> Self {
        ScanConfig {
            targets,
            ..Default::default()
        }
    }

    /// Set specific ports to scan
    pub fn with_ports(mut self, ports: Vec<u16>) -> Self {
        self.ports = Some(ports);
        self.range = None; // Clear range when specific ports are set
        self
    }

    /// Set port range to scan
    pub fn with_range(mut self, start: u16, end: u16) -> Self {
        self.range = Some(PortRange { start, end });
        self.ports = None; // Clear specific ports when range is set
        self
    }

    /// Set batch size
    pub fn with_batch_size(mut self, batch_size: u16) -> Self {
        self.batch_size = batch_size;
        self
    }

    /// Set timeout
    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.timeout = timeout;
        self
    }

    /// Set scan order
    pub fn with_scan_order(mut self, order: ScanOrder) -> Self {
        self.scan_order = order;
        self
    }

    /// Enable UDP scanning
    pub fn with_udp(mut self, udp: bool) -> Self {
        self.udp = udp;
        self
    }

    /// Set ports to exclude
    pub fn with_exclude_ports(mut self, exclude_ports: Vec<u16>) -> Self {
        self.exclude_ports = exclude_ports;
        self
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<(), String> {
        if self.targets.is_empty() {
            return Err("No targets specified".to_string());
        }

        if self.ports.is_none() && self.range.is_none() {
            return Err("Either ports or range must be specified".to_string());
        }

        if let Some(range) = &self.range {
            if range.start > range.end {
                return Err("Invalid port range: start > end".to_string());
            }
            if range.start == 0 {
                return Err("Port range cannot start from 0".to_string());
            }
        }

        if let Some(ports) = &self.ports {
            if ports.is_empty() {
                return Err("Port list cannot be empty".to_string());
            }
            if ports.iter().any(|&p| p == 0) {
                return Err("Port 0 is not valid".to_string());
            }
        }

        if self.batch_size == 0 {
            return Err("Batch size cannot be 0".to_string());
        }

        if self.tries == 0 {
            return Err("Tries cannot be 0".to_string());
        }

        Ok(())
    }
}

/// Scan type enumeration for different scanning modes
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ScanType {
    /// Quick scan - common ports only
    Quick,
    /// Standard scan - balanced speed and coverage
    Standard,
    /// Deep scan - comprehensive scanning
    Deep,
    /// Web-focused scan - HTTP/HTTPS specific
    Web,
    /// Custom scan - user-defined parameters
    Custom,
}

impl Default for ScanType {
    fn default() -> Self {
        ScanType::Standard
    }
}

impl ScanType {
    /// Get the default port range for this scan type
    pub fn default_ports(&self) -> Vec<u16> {
        match self {
            ScanType::Quick => vec![
                21, 22, 23, 25, 53, 80, 110, 111, 135, 139, 143, 443, 993, 995, 1723, 3306, 3389,
                5900, 8080,
            ],
            ScanType::Standard => (1..=1000).collect(),
            ScanType::Deep => (1..=65535).collect(),
            ScanType::Web => vec![80, 443, 8000, 8080, 8443, 8888, 9000, 9443],
            ScanType::Custom => Vec::new(),
        }
    }

    /// Get the default timeout for this scan type
    pub fn default_timeout(&self) -> Duration {
        match self {
            ScanType::Quick => Duration::from_millis(500),
            ScanType::Standard => Duration::from_millis(1500),
            ScanType::Deep => Duration::from_millis(3000),
            ScanType::Web => Duration::from_millis(2000),
            ScanType::Custom => Duration::from_millis(1500),
        }
    }

    /// Get the default batch size for this scan type
    pub fn default_batch_size(&self) -> u16 {
        match self {
            ScanType::Quick => 1000,
            ScanType::Standard => 4500,
            ScanType::Deep => 2000,
            ScanType::Web => 500,
            ScanType::Custom => 4500,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scan_config_validation() {
        let mut config = ScanConfig::new(vec!["127.0.0.1".to_string()]);
        assert!(config.validate().is_ok());

        // Test empty targets
        config.targets.clear();
        assert!(config.validate().is_err());

        // Test invalid port range
        config.targets = vec!["127.0.0.1".to_string()];
        config.range = Some(PortRange {
            start: 100,
            end: 50,
        });
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_scan_type_defaults() {
        assert_eq!(ScanType::Quick.default_ports().len(), 19);
        assert_eq!(ScanType::Standard.default_ports().len(), 1000);
        assert!(ScanType::Deep.default_timeout() > ScanType::Quick.default_timeout());
    }
}

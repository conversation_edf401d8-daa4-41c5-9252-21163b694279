//! Core address parsing and resolution functionality
//!
//! This module provides functions to parse IP addresses, CIDRs, hostnames,
//! and files containing addresses. Adapted from the original RustScan address module.

use std::collections::BTreeSet;
use std::fs::{self, File};
use std::io::{prelude::*, BufReader};
use std::net::{IpAddr, SocketAddr, ToSocketAddrs};
use std::path::Path;
use std::str::FromStr;

use cidr_utils::cidr::{IpCidr, IpInet};
use hickory_resolver::{
    config::{NameServerConfig, Protocol, ResolverConfig, ResolverOpts},
    Resolver,
};
use tracing::{debug, warn};

use super::config::ScanConfig;
use crate::common::error::{AppError, Result};

/// Parse addresses from a scan configuration
pub fn parse_addresses_from_config(config: &ScanConfig) -> Result<Vec<IpAddr>> {
    let resolver = get_resolver(&config.resolvers)?;
    let mut ips: Vec<IpAddr> = Vec::new();
    let mut unresolved_addresses: Vec<&str> = Vec::new();

    for address in &config.targets {
        let parsed_ips = parse_address(address, &resolver)?;
        if !parsed_ips.is_empty() {
            ips.extend(parsed_ips);
        } else {
            unresolved_addresses.push(address);
        }
    }

    // Try to parse unresolved addresses as file paths
    for file_path in unresolved_addresses {
        let file_path = Path::new(file_path);

        if !file_path.is_file() {
            warn!("Host {:?} could not be resolved", file_path);
            continue;
        }

        match read_ips_from_file(file_path, &resolver) {
            Ok(file_ips) => ips.extend(file_ips),
            Err(e) => {
                warn!("Failed to read IPs from file {:?}: {}", file_path, e);
            }
        }
    }

    // Parse excluded networks
    let excluded_cidrs = if let Some(ref exclude_addresses) = config.exclude_addresses {
        parse_excluded_networks(exclude_addresses, &resolver)?
    } else {
        Vec::new()
    };

    // Remove duplicated/excluded IPs
    let mut seen = BTreeSet::new();
    ips.retain(|ip| seen.insert(*ip) && !excluded_cidrs.iter().any(|cidr| cidr.contains(ip)));

    Ok(ips)
}

/// Parse a single address string into IP addresses
pub fn parse_address(address: &str, resolver: &Resolver) -> Result<Vec<IpAddr>> {
    if let Ok(addr) = IpAddr::from_str(address) {
        // `address` is an IP string
        Ok(vec![addr])
    } else if let Ok(net_addr) = IpInet::from_str(address) {
        // `address` is a CIDR string
        Ok(net_addr.network().into_iter().addresses().collect())
    } else {
        // `address` is a hostname or DNS name
        // attempt default DNS lookup first
        match format!("{address}:80").to_socket_addrs() {
            Ok(mut iter) => {
                if let Some(socket_addr) = iter.next() {
                    Ok(vec![socket_addr.ip()])
                } else {
                    Ok(Vec::new())
                }
            }
            // default lookup didn't work, so try again with the dedicated resolver
            Err(_) => resolve_ips_from_host(address, resolver),
        }
    }
}

/// Use DNS to get the IPs associated with a hostname
fn resolve_ips_from_host(source: &str, backup_resolver: &Resolver) -> Result<Vec<IpAddr>> {
    let mut ips: Vec<IpAddr> = Vec::new();

    // Try standard library resolution first
    if let Ok(addrs) = source.to_socket_addrs() {
        for ip in addrs {
            ips.push(ip.ip());
        }
    } else if let Ok(addrs) = backup_resolver.lookup_ip(source) {
        // Use the dedicated resolver
        ips.extend(addrs.iter());
    }

    Ok(ips)
}

/// Parse excluded networks from a list of addresses
pub fn parse_excluded_networks(
    exclude_addresses: &[String],
    resolver: &Resolver,
) -> Result<Vec<IpCidr>> {
    let mut excluded_cidrs = Vec::new();
    
    for addr in exclude_addresses {
        let cidrs = parse_single_excluded_address(addr, resolver)?;
        excluded_cidrs.extend(cidrs);
    }
    
    Ok(excluded_cidrs)
}

/// Parse a single address into an IpCidr, handling CIDR notation, IP addresses, and hostnames
fn parse_single_excluded_address(addr: &str, resolver: &Resolver) -> Result<Vec<IpCidr>> {
    if let Ok(cidr) = IpCidr::from_str(addr) {
        return Ok(vec![cidr]);
    }

    if let Ok(ip) = IpAddr::from_str(addr) {
        return Ok(vec![IpCidr::new_host(ip)]);
    }

    let ips = resolve_ips_from_host(addr, resolver)?;
    Ok(ips.into_iter().map(IpCidr::new_host).collect())
}

/// Create a DNS resolver based on configuration
fn get_resolver(resolver_config: &Option<Vec<String>>) -> Result<Resolver> {
    match resolver_config {
        Some(resolvers) => {
            let mut config = ResolverConfig::new();
            
            for resolver_str in resolvers {
                // Try to parse as file path first
                let resolver_ips = match read_resolver_from_file(resolver_str) {
                    Ok(ips) => ips,
                    Err(_) => {
                        // Parse as comma-separated list of IPs
                        resolver_str
                            .split(',')
                            .filter_map(|r| IpAddr::from_str(r.trim()).ok())
                            .collect::<Vec<_>>()
                    }
                };
                
                for ip in resolver_ips {
                    config.add_name_server(NameServerConfig::new(
                        SocketAddr::new(ip, 53),
                        Protocol::Udp,
                    ));
                }
            }
            
            Resolver::new(config, ResolverOpts::default())
                .map_err(|e| AppError::NetworkError(format!("Failed to create resolver: {}", e)))
        }
        None => {
            // Try system resolver first, fallback to Cloudflare
            match Resolver::from_system_conf() {
                Ok(resolver) => Ok(resolver),
                Err(_) => {
                    debug!("Using Cloudflare DNS resolver as fallback");
                    Resolver::new(ResolverConfig::cloudflare_tls(), ResolverOpts::default())
                        .map_err(|e| AppError::NetworkError(format!("Failed to create resolver: {}", e)))
                }
            }
        }
    }
}

/// Parse resolver IPs from a file
fn read_resolver_from_file(path: &str) -> Result<Vec<IpAddr>> {
    let content = fs::read_to_string(path)
        .map_err(|e| AppError::ConfigError(format!("Failed to read resolver file {}: {}", path, e)))?;
    
    let ips = content
        .lines()
        .filter_map(|line| IpAddr::from_str(line.trim()).ok())
        .collect();

    Ok(ips)
}

/// Parse IPs from a file
fn read_ips_from_file(file_path: &Path, backup_resolver: &Resolver) -> Result<Vec<IpAddr>> {
    let file = File::open(file_path)
        .map_err(|e| AppError::ConfigError(format!("Failed to open file {:?}: {}", file_path, e)))?;
    let reader = BufReader::new(file);

    let mut ips: Vec<IpAddr> = Vec::new();

    for address_line in reader.lines() {
        match address_line {
            Ok(address) => {
                let parsed_ips = parse_address(&address, backup_resolver)?;
                ips.extend(parsed_ips);
            }
            Err(e) => {
                debug!("Failed to read line from file: {}", e);
            }
        }
    }

    Ok(ips)
}

/// Validate that an IP address is scannable (not localhost, multicast, etc.)
pub fn is_scannable_ip(ip: &IpAddr) -> bool {
    match ip {
        IpAddr::V4(ipv4) => {
            !ipv4.is_loopback()
                && !ipv4.is_multicast()
                && !ipv4.is_broadcast()
                && !ipv4.is_unspecified()
                && !ipv4.is_link_local()
        }
        IpAddr::V6(ipv6) => {
            !ipv6.is_loopback()
                && !ipv6.is_multicast()
                && !ipv6.is_unspecified()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::Ipv4Addr;

    #[test]
    fn test_parse_ip_address() {
        let resolver = Resolver::from_system_conf().unwrap();
        let ips = parse_address("127.0.0.1", &resolver).unwrap();
        assert_eq!(ips, vec![IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1))]);
    }

    #[test]
    fn test_parse_cidr() {
        let resolver = Resolver::from_system_conf().unwrap();
        let ips = parse_address("***********/30", &resolver).unwrap();
        assert_eq!(ips.len(), 4);
        assert!(ips.contains(&IpAddr::V4(Ipv4Addr::new(192, 168, 0, 0))));
        assert!(ips.contains(&IpAddr::V4(Ipv4Addr::new(192, 168, 0, 3))));
    }

    #[test]
    fn test_is_scannable_ip() {
        assert!(!is_scannable_ip(&IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)))); // localhost
        assert!(!is_scannable_ip(&IpAddr::V4(Ipv4Addr::new(224, 0, 0, 1)))); // multicast
        assert!(is_scannable_ip(&IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8)))); // public IP
    }
}

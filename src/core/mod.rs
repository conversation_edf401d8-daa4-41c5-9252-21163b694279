//! Core scanning functionality
//!
//! This module contains the core scanning logic migrated from the original RustScan,
//! adapted for use in both CLI and web contexts.

pub mod address;
pub mod config;
pub mod port_strategy;
pub mod scanner;

// Re-export commonly used types
pub use address::parse_addresses_from_config;
pub use config::{<PERSON><PERSON><PERSON><PERSON>, ScanConfig, ScanOrder};
pub use port_strategy::PortStrategy;
pub use scanner::Scanner;

/// Core scanning result type
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ScanResult {
    pub target: String,
    pub open_ports: Vec<u16>,
    pub scan_duration: std::time::Duration,
    pub scan_type: String,
}

/// Scanning error types
#[derive(Debug, thiserror::Error)]
pub enum ScanError {
    #[error("Invalid target address: {0}")]
    InvalidTarget(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Timeout error: {0}")]
    TimeoutError(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),
}

pub type Result<T> = std::result::Result<T, ScanError>;

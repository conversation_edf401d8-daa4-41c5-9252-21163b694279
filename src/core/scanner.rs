//! Core scanning functionality
//!
//! This module provides the main scanning engine adapted from the original RustScan,
//! optimized for use in both CLI and web contexts.

use std::collections::HashSet;
use std::net::{IpAddr, SocketAddr};
use std::num::NonZeroU8;
use std::time::Duration;

use futures::stream::FuturesUnordered;
use futures::StreamExt;
use tokio::net::{TcpStream, UdpSocket};
use tokio::time::timeout;
use tracing::{debug, warn};

use super::address::parse_addresses_from_config;
use super::config::ScanConfig;
use super::port_strategy::PortStrategy;
use crate::common::error::{AppError, Result};

/// Core scanner implementation
#[derive(Debug)]
pub struct Scanner {
    ips: Vec<IpAddr>,
    batch_size: u16,
    timeout: Duration,
    tries: NonZeroU8,
    port_strategy: PortStrategy,
    exclude_ports: Vec<u16>,
    udp: bool,
}

impl Scanner {
    /// Create a new scanner from configuration
    pub async fn from_config(config: &ScanConfig) -> Result<Self> {
        config.validate().map_err(|e| AppError::validation(e))?;

        let ips = parse_addresses_from_config(config)?;
        if ips.is_empty() {
            return Err(AppError::validation("No valid IP addresses found"));
        }

        let port_strategy =
            PortStrategy::pick(&config.range, config.ports.clone(), config.scan_order);

        Ok(Scanner {
            ips,
            batch_size: config.batch_size,
            timeout: config.timeout,
            tries: NonZeroU8::new(std::cmp::max(config.tries, 1)).unwrap(),
            port_strategy,
            exclude_ports: config.exclude_ports.clone(),
            udp: config.udp,
        })
    }

    /// Create a new scanner with explicit parameters
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        ips: &[IpAddr],
        batch_size: u16,
        timeout: Duration,
        tries: u8,
        port_strategy: PortStrategy,
        exclude_ports: Vec<u16>,
        udp: bool,
    ) -> Self {
        Self {
            ips: ips.to_vec(),
            batch_size,
            timeout,
            tries: NonZeroU8::new(std::cmp::max(tries, 1)).unwrap(),
            port_strategy,
            exclude_ports,
            udp,
        }
    }

    /// Run the scan and return open sockets
    pub async fn run(&self) -> Vec<SocketAddr> {
        let ports: Vec<u16> = self
            .port_strategy
            .order()
            .iter()
            .filter(|&port| !self.exclude_ports.contains(port))
            .copied()
            .collect();

        let mut socket_iterator = SocketIterator::new(&self.ips, &ports);
        let mut open_sockets: Vec<SocketAddr> = Vec::new();
        let mut ftrs = FuturesUnordered::new();
        let mut errors: HashSet<String> = HashSet::new();

        // Initialize the futures pool
        for _ in 0..self.batch_size {
            if let Some(socket) = socket_iterator.next() {
                ftrs.push(self.scan_socket(socket));
            } else {
                break;
            }
        }

        debug!(
            "Starting scan: batch_size={}, ips={}, ports={}, total_targets={}",
            self.batch_size,
            self.ips.len(),
            ports.len(),
            self.ips.len() * ports.len()
        );

        // Process results and maintain the futures pool
        while let Some(result) = ftrs.next().await {
            // Add new socket to scan if available
            if let Some(socket) = socket_iterator.next() {
                ftrs.push(self.scan_socket(socket));
            }

            match result {
                Ok(socket) => {
                    debug!("Found open port: {}", socket);
                    open_sockets.push(socket);
                }
                Err(e) => {
                    let error_string = e.to_string();
                    if errors.len() < self.ips.len() * 100 {
                        errors.insert(error_string);
                    }
                }
            }
        }

        if !errors.is_empty() {
            debug!("Scan completed with {} unique error types", errors.len());
        }

        debug!("Scan completed. Found {} open ports", open_sockets.len());
        open_sockets
    }

    /// Scan a single socket
    async fn scan_socket(&self, socket: SocketAddr) -> Result<SocketAddr> {
        if self.udp {
            return self.scan_udp_socket(socket).await;
        }

        let tries = self.tries.get();
        for attempt in 1..=tries {
            match self.connect_tcp(socket).await {
                Ok(stream) => {
                    debug!("TCP connection successful to {}", socket);
                    drop(stream); // Close the connection
                    return Ok(socket);
                }
                Err(e) => {
                    if attempt == tries {
                        return Err(AppError::network(format!(
                            "Failed to connect to {}: {}",
                            socket, e
                        )));
                    }
                    debug!(
                        "TCP connection attempt {} failed for {}: {}",
                        attempt, socket, e
                    );
                }
            }
        }

        unreachable!()
    }

    /// Scan a UDP socket
    async fn scan_udp_socket(&self, socket: SocketAddr) -> Result<SocketAddr> {
        let payload = self.get_udp_payload(socket.port());
        let tries = self.tries.get();

        for attempt in 1..=tries {
            match self.udp_scan(socket, &payload).await {
                Ok(true) => {
                    debug!("UDP response received from {}", socket);
                    return Ok(socket);
                }
                Ok(false) => {
                    debug!("UDP scan attempt {} timed out for {}", attempt, socket);
                    continue;
                }
                Err(e) => {
                    return Err(AppError::network(format!(
                        "UDP scan error for {}: {}",
                        socket, e
                    )));
                }
            }
        }

        Err(AppError::network(format!(
            "UDP scan timed out for all {} attempts on {}",
            tries, socket
        )))
    }

    /// Connect to a TCP socket with timeout
    async fn connect_tcp(&self, socket: SocketAddr) -> std::io::Result<TcpStream> {
        timeout(self.timeout, TcpStream::connect(socket)).await?
    }

    /// Perform UDP scan on a socket
    async fn udp_scan(&self, socket: SocketAddr, payload: &[u8]) -> std::io::Result<bool> {
        let local_addr = match socket {
            SocketAddr::V4(_) => "0.0.0.0:0",
            SocketAddr::V6(_) => "[::]:0",
        };

        let udp_socket = UdpSocket::bind(local_addr).await?;
        udp_socket.connect(socket).await?;
        udp_socket.send(payload).await?;

        let mut buf = [0u8; 1024];
        match timeout(self.timeout, udp_socket.recv(&mut buf)).await {
            Ok(Ok(size)) => {
                debug!("Received {} bytes from UDP socket {}", size, socket);
                Ok(true)
            }
            Ok(Err(e)) => Err(e),
            Err(_) => Ok(false), // Timeout
        }
    }

    /// Get UDP payload for specific port
    fn get_udp_payload(&self, port: u16) -> Vec<u8> {
        // Basic UDP payloads for common services
        match port {
            53 => b"\x12\x34\x01\x00\x00\x01\x00\x00\x00\x00\x00\x00\x07example\x03com\x00\x00\x01\x00\x01".to_vec(), // DNS query
            123 => b"\x1b\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00".to_vec(), // NTP
            161 => b"\x30\x26\x02\x01\x01\x04\x06public\xa0\x19\x02\x04\x00\x00\x00\x00\x02\x01\x00\x02\x01\x00\x30\x0b\x30\x09\x06\x05\x2b\x06\x01\x02\x01\x05\x00".to_vec(), // SNMP
            _ => Vec::new(), // Empty payload for other ports
        }
    }

    /// Get scan statistics
    pub fn get_stats(&self) -> ScanStats {
        ScanStats {
            total_ips: self.ips.len(),
            total_ports: self.port_strategy.port_count(),
            total_targets: self.ips.len() * self.port_strategy.port_count(),
            batch_size: self.batch_size as usize,
            timeout_ms: self.timeout.as_millis() as u64,
            tries: self.tries.get() as usize,
            udp_scan: self.udp,
        }
    }
}

/// Socket iterator for generating IP:port combinations
struct SocketIterator {
    ips: Vec<IpAddr>,
    ports: Vec<u16>,
    ip_index: usize,
    port_index: usize,
}

impl SocketIterator {
    fn new(ips: &[IpAddr], ports: &[u16]) -> Self {
        Self {
            ips: ips.to_vec(),
            ports: ports.to_vec(),
            ip_index: 0,
            port_index: 0,
        }
    }
}

impl Iterator for SocketIterator {
    type Item = SocketAddr;

    fn next(&mut self) -> Option<Self::Item> {
        if self.port_index >= self.ports.len() {
            return None;
        }

        let socket = SocketAddr::new(self.ips[self.ip_index], self.ports[self.port_index]);

        self.ip_index += 1;
        if self.ip_index >= self.ips.len() {
            self.ip_index = 0;
            self.port_index += 1;
        }

        Some(socket)
    }
}

/// Scan statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ScanStats {
    pub total_ips: usize,
    pub total_ports: usize,
    pub total_targets: usize,
    pub batch_size: usize,
    pub timeout_ms: u64,
    pub tries: usize,
    pub udp_scan: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::config::{PortRange, ScanConfig, ScanOrder};

    #[tokio::test]
    async fn test_scanner_creation() {
        let mut config = ScanConfig::new(vec!["127.0.0.1".to_string()]);
        config.range = Some(PortRange { start: 80, end: 80 });

        let scanner = Scanner::from_config(&config).await.unwrap();
        assert_eq!(scanner.ips.len(), 1);
        assert_eq!(scanner.batch_size, 4500);
    }

    #[test]
    fn test_socket_iterator() {
        let ips = vec!["127.0.0.1".parse().unwrap(), "*********".parse().unwrap()];
        let ports = vec![80, 443];
        let mut iter = SocketIterator::new(&ips, &ports);

        assert_eq!(iter.next(), Some(SocketAddr::new(ips[0], 80)));
        assert_eq!(iter.next(), Some(SocketAddr::new(ips[1], 80)));
        assert_eq!(iter.next(), Some(SocketAddr::new(ips[0], 443)));
        assert_eq!(iter.next(), Some(SocketAddr::new(ips[1], 443)));
        assert_eq!(iter.next(), None);
    }
}

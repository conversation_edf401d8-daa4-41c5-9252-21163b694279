//! RustScan Web - Main entry point for the web server
//!
//! This is the main entry point for the RustScan Web security scanning platform.
//! It starts the Actix-web server and initializes all necessary services.

use actix_cors::Cors;
use actix_web::{middleware::Logger, web, App, HttpServer};
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use rustscan_web::{
    common::{config::AppConfig, error::Result},
    database::get_database_pool,
    services::{NotificationService, ResultService, ScanService, TaskService},
    web::routes,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    init_tracing();

    info!("Starting RustScan Web Security Scanning Platform");

    // Load configuration
    let config = AppConfig::load().await?;
    info!("Configuration loaded successfully");

    // Initialize database
    let db_pool = get_database_pool(&config.database_url).await?;
    info!("Database connection established");

    // Run database migrations
    sqlx::migrate!("./migrations")
        .run(&db_pool)
        .await
        .map_err(|e| {
            error!("Failed to run database migrations: {}", e);
            rustscan_web::common::error::AppError::DatabaseError(e.to_string())
        })?;
    info!("Database migrations completed");

    // Initialize services
    let task_service = TaskService::new(db_pool.clone());
    let scan_service = ScanService::new();
    let result_service = ResultService::new(db_pool.clone());
    let notification_service = NotificationService::new();

    info!("Services initialized successfully");

    // Create and start HTTP server
    let bind_address = format!("{}:{}", config.host, config.port);
    info!("Starting HTTP server on {}", bind_address);

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(db_pool.clone()))
            .app_data(web::Data::new(task_service.clone()))
            .app_data(web::Data::new(scan_service.clone()))
            .app_data(web::Data::new(result_service.clone()))
            .app_data(web::Data::new(notification_service.clone()))
            .wrap(Logger::default())
            .wrap(
                Cors::default()
                    .allow_any_origin()
                    .allow_any_method()
                    .allow_any_header()
                    .max_age(3600),
            )
            .configure(routes::configure_routes)
    })
    .bind(&bind_address)?
    .run()
    .await
    .map_err(|e| {
        error!("HTTP server error: {}", e);
        rustscan_web::common::error::AppError::ServerError(e.to_string())
    })?;

    Ok(())
}

fn init_tracing() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "rustscan_web=info,actix_web=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}

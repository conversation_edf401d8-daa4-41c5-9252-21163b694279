[package]
name = "rustscan-web"
version = "2.4.1"
authors = ["Autumn <<EMAIL>>"]
edition = "2021"
description = "Modern Web Security Scanning Platform based on RustScan"
homepage = "https://github.com/rustscan/rustscan"
repository = "https://github.com/rustscan/rustscan"
license = "GPL-3.0-only"
keywords = ["security", "scanning", "web", "nmap", "vulnerability"]
categories = ["web-programming", "command-line-utilities"]
readme = "README.md"
exclude = [
    ".github/*",
    "pictures/*",
    "rustscan-debbuilder/*",
    "frontend/node_modules/*",
    "frontend/dist/*",
]

[dependencies]
# Web framework
actix-web = "4.9"
actix-web-actors = "4.3"
actix-cors = "0.7"

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid", "json"] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.9.0"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-actix-web = "0.7"

# Core scanning functionality (from original RustScan)
async-std = "1.13.1"
cidr-utils = "0.6.1"
hickory-resolver = { version = "0.24.3", features = ["dns-over-rustls"] }
itertools = "0.14.0"
rand = "0.9.1"
text_placeholder = { version = "0.5", features = ["struct_context"] }
once_cell = "1.21.3"
rlimit = "0.10.2"
gcd = "2.0.1"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
dirs = "6.0.0"

# Additional dependencies
tokio-util = "0.7"
futures-util = "0.3"

# CLI support (optional)
clap = { version = "4.5.41", features = ["derive", "wrap_help"], optional = true }
colored = { version = "3.0.0", optional = true }
colorful = { version = "0.3.2", optional = true }
ansi_term = { version = "0.12.1", optional = true }
env_logger = { version = "0.11.8", optional = true }
log = { version = "0.4.27", optional = true }
serde_derive = { version = "1.0", optional = true }

[dev-dependencies]
parameterized = "2.0.0"
wait-timeout = "0.2"
criterion = { version = "0.6", features = ["html_reports"] }

[package.metadata.deb]
depends = "$auto, nmap"
section = "rust"

[profile.release]
lto = true
panic = 'abort'
strip = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
# Features
[features]
default = ["web"]
web = []
cli = ["clap", "colored", "colorful", "ansi_term", "env_logger", "log", "serde_derive"]

# Binaries
[[bin]]
name = "rustscan-web"
path = "src/main.rs"
required-features = ["web"]

[[bin]]
name = "rustscan-cli"
path = "src/cli.rs"
required-features = ["cli"]

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ["cfg(tarpaulin_include)"] }

[[bench]]
name = "benchmark_portscan"
harness = false
